#!/bin/bash

echo "=== Flask Container Startup Script ==="

# 等待Redis启动
echo "Waiting for Red<PERSON> to be ready..."
while ! nc -z redis 6379; do
    echo "Redis not ready, waiting..."
    sleep 1
done
echo "Redis is ready!"

# 生成SSH密钥
echo "Generating SSH keys..."
python generate_key.py

# 检查密钥文件是否正确生成
echo "Checking generated key files..."

if [ -f "/volume/key/id_rsa" ]; then
    echo "✓ Private key exists"
    chmod 600 /volume/key/id_rsa
    echo "✓ Private key permissions set"
else
    echo "✗ Private key missing!"
    exit 1
fi

if [ -f "/volume/authorized_keys" ]; then
    echo "✓ Public key exists"
    chmod 644 /volume/authorized_keys
    echo "✓ Public key permissions set"
    echo "Public key content:"
    cat /volume/authorized_keys
else
    echo "✗ Public key missing!"
    exit 1
fi

# 显示文件信息
echo "Key files status:"
ls -la /volume/key/
ls -la /volume/authorized_keys

echo "=== Starting Flask Application ==="
exec gunicorn -c gunicorn.py main:app
