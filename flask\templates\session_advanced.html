<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <title>Supershell - 进阶功能</title>
    <link rel="icon" href="/static/img/favicon.ico" type="image/x-icon">
    <link href="/static/css/tabler.min.css" rel="stylesheet"/>
    <link href="/static/css/toastr.min.css" rel="stylesheet"/>
    <style>
      @import url('https://rsms.me/inter/inter.css');
      :root {
      	--tblr-font-sans-serif: 'Inter Var', -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;
      }
      body {
      	font-feature-settings: "cv03", "cv04", "cv11";
      }
    </style>
  </head>
  <body class="layout-fluid">
    <script src="/static/js/demo-theme.min.js"></script>
    <div class="page">
      <header class="navbar navbar-expand-md navbar-light d-print-none">
        <div class="container-xl">
          <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu" aria-controls="navbar-menu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
          </button>
          <h1 class="navbar-brand navbar-brand-autodark d-none-navbar-horizontal pe-0 pe-md-3">
            <a href="/black/monitor">
              <img src="/static/img/logo.svg" width="110" height="32" alt="Black" class="navbar-brand-image">
            </a>
          </h1>
          <div class="navbar-nav flex-row order-md-last">
            <div class="d-none d-md-flex me-3">
              <a href="?arg={{ sessid | safe }}&theme=dark" class="nav-link px-0 hide-theme-dark" title="暗黑模式" data-bs-toggle="tooltip"
		   data-bs-placement="bottom">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" /></svg>
              </a>
              <a href="?arg={{ sessid | safe }}&theme=light" class="nav-link px-0 hide-theme-light" title="明亮模式" data-bs-toggle="tooltip"
		   data-bs-placement="bottom">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="12" cy="12" r="4" /><path d="M3 12h1m8 -9v1m8 8h1m-9 8v1m-6.4 -15.4l.7 .7m12.1 -.7l-.7 .7m0 11.4l.7 .7m-12.1 -.7l-.7 .7" /></svg>
              </a>
            </div>
            <div class="nav-item dropdown">
              <a href="javascript:void(0)" class="nav-link d-flex lh-1 text-reset p-0" data-bs-toggle="dropdown">
                <span class="avatar avatar-sm" style="background-image: url(/static/img/avatar.jpg)"></span>
                <div class="d-none d-xl-block ps-2">
                  <div>{{ username }}</div>
                  <div class="mt-1 small text-muted">Hacker</div>
                </div>
              </a>
              <div class="dropdown-menu dropdown-menu-arrow">
                <a href="/black/setting" target="_blank" class="dropdown-item">
                  <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-settings" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z"></path>
                    <path d="M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"></path>
                  </svg>&nbsp;
                  设置
                </a>
                <a href="https://github.com/tdragon6/Supershell" target="_blank" class="dropdown-item">
                  <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-brand-github" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M9 19c-4.3 1.4 -4.3 -2.5 -6 -3m12 5v-3.5c0 -1 .1 -1.4 -.5 -2c2.8 -.3 5.5 -1.4 5.5 -6a4.6 4.6 0 0 0 -1.3 -3.2a4.2 4.2 0 0 0 -.1 -3.2s-1.1 -.3 -3.5 1.3a12.3 12.3 0 0 0 -6.2 0c-2.4 -1.6 -3.5 -1.3 -3.5 -1.3a4.2 4.2 0 0 0 -.1 3.2a4.6 4.6 0 0 0 -1.3 3.2c0 4.6 2.7 5.7 5.5 6c-.6 .6 -.6 1.2 -.5 2v3.5"></path>
                  </svg>&nbsp;
                  Github
                </a>
                <div class="dropdown-divider"></div>
                <a href="javascript:void(0);" onclick="$.removeCookie('token',{ path: '/'});location.reload();" class="dropdown-item">
                  <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-logout" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M14 8v-2a2 2 0 0 0 -2 -2h-7a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2 -2v-2"></path>
                    <path d="M7 12h14l-3 -3m0 6l3 -3"></path>
                  </svg>&nbsp;
                  注销
                </a>
              </div>
            </div>
          </div>
        </div>
      </header>
      <header class="navbar-expand-md">
        <div class="collapse navbar-collapse" id="navbar-menu">
          <div class="navbar navbar-light">
            <div class="container-xl">
              <ul class="navbar-nav">
                <li class="nav-item">
                  <a class="nav-link" href="" id="session-info">
                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-info-square-rounded" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M12 8h.01"></path>
                        <path d="M11 12h1v4h1"></path>
                        <path d="M12 3c7.2 0 9 1.8 9 9s-1.8 9 -9 9s-9 -1.8 -9 -9s1.8 -9 9 -9z"></path>
                      </svg>
                    </span>
                    <span class="nav-link-title">
                      会话信息
                    </span>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" href="" id="session-shell">
                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-brand-tabler" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                         <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                         <path d="M8 9l3 3l-3 3"></path>
                         <path d="M13 15l3 0"></path>
                         <path d="M4 4m0 4a4 4 0 0 1 4 -4h8a4 4 0 0 1 4 4v8a4 4 0 0 1 -4 4h-8a4 4 0 0 1 -4 -4z"></path>
                      </svg>
                    </span>
                    <span class="nav-link-title">
                      交互终端
                    </span>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" href="" id="session-files">
                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-file-description" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M14 3v4a1 1 0 0 0 1 1h4"></path>
                        <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"></path>
                        <path d="M9 17h6"></path>
                        <path d="M9 13h6"></path>
                      </svg>
                    </span>
                    <span class="nav-link-title">
                      文件管理
                    </span>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" href="" id="session-memfd">
                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-file-x" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                         <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                         <path d="M14 3v4a1 1 0 0 0 1 1h4"></path>
                         <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"></path>
                       <path d="M10 12l4 4m0 -4l-4 4"></path>
                      </svg>
                    </span>
                    <span class="nav-link-title">
                      内存执行
                    </span>
                  </a>
                </li>
                <li class="nav-item active">
                  <a class="nav-link" href="" id="session-advanced">
                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-swords" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                         <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                         <path d="M21 3v5l-11 9l-4 4l-3 -3l4 -4l9 -11z"></path>
                         <path d="M5 13l6 6"></path>
                         <path d="M14.32 17.32l3.68 3.68l3 -3l-3.365 -3.365"></path>
                         <path d="M10 5.5l-2 -2.5h-5v5l3 2.5"></path>
                      </svg>
                    </span>
                    <span class="nav-link-title">
                      进阶功能
                    </span>
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </header>
      <div class="page-wrapper">
        <div class="page-body">
          <div class="container-xl">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">进阶功能</h3>
                &nbsp;&nbsp;
                <span id="loader">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-check" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M5 12l5 5l10 -10"></path>
                    </svg>
                </span>
              </div>
              <div class="card-body row">
                <div class="col-5">
                  <h3 class="card-title">Windows安装服务</h3>
                  <p class="card-subtitle mt-3">仅限Windows操作系统，安装一个额外的反弹shell服务，成功后会新增一个客户端连接，卸载服务不会断开该连接，需卸载服务且断开连接后安装同名服务才会生效。</p>
                  <div class="mb-5">
                    <div class="row g-2">
                      <div class="col-auto">
                        <input id="service-sub" type="text" class="form-control w-auto" placeholder="服务名">
                      </div>
                      <div class="col-auto">
                          <a href="javascript:void(0);" onclick="serviceFunc(sessid, 'install');" class="btn btn-primary">安装</a>
                      </div>
                      <div class="col-auto">
                          <a href="javascript:void(0);" onclick="serviceFunc(sessid, 'uninstall');" class="btn btn-danger">卸载</a>
                      </div>
                    </div>
                  </div>
                  <h3 class="card-title">Linux提权</h3>
                  <p class="card-subtitle mt-3">仅限Linux操作系统，尝试将会话迁移至给定的uid或gid，默认均为0。</p>
                  <div class="mb-5">
                    <div class="row g-2 mb-3">
                      <div class="col-auto">
                        <input id="uid-sub" type="text" class="form-control w-auto" placeholder="uid">
                      </div>
                      <div class="col-auto">
                          <a href="javascript:void(0);" onclick="set_ugid(sessid, 'uid');" class="btn btn-primary">确定</a>
                      </div>
                    </div>
                    <div class="row g-2 mb-3">
                      <div class="col-auto">
                        <input id="gid-sub" type="text" class="form-control w-auto" placeholder="gid">
                      </div>
                      <div class="col-auto">
                          <a href="javascript:void(0);" onclick="set_ugid(sessid, 'gid');" class="btn btn-primary">确定</a>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-1"></div>
                <div class="col-5">
                  <h3 class="card-title">开启监听</h3>
                  <p class="card-subtitle mt-3">在目标主机上开启一个回连监听，对于其内网中的其他主机，可以连接此监听地址进行回连（可适用于内网中不出网主机的回连），全局监听时地址可不填。<br><br>使用举例:<br>1、当前主机可出网，内网地址为 *************，开启端口为1111的监听。<br>2、另一台与其在同一内网段的主机无法出网。<br>3、此时可通过 ./client -d *************:1111 回连，同时也可在客户端生成中设置回连地址和端口。</p>
                  <div class="mb-5">
                    <div class="row g-2 mb-3">
                      <div class="col-auto">
                        <input id="address-proxy" type="text" class="form-control w-auto" placeholder="监听地址">
                      </div>
                      <div class="col-auto">
                        <input id="port-proxy" type="text" class="form-control w-auto" placeholder="监听端口">
                      </div>
                      <div class="col-auto">
                          <a href="javascript:void(0);" onclick="listenFunc(sessid, 'on');" class="btn btn-primary">开启</a>
                      </div>
                      <div class="col-auto">
                        <a href="javascript:void(0);" onclick="listenFunc(sessid, 'off');" class="btn btn-danger">关闭</a>
                      </div>
                    </div>
                    <div class="row g-2 mb-3">
                      <div class="col-auto">
                        <a href="javascript:void(0);" onclick="showListenedFunc(sessid);" class="btn btn-sm btn-outline-white">查看当前会话监听</a>
                      </div>
                      <div class="col-auto">
                        <a href="javascript:void(0);" onclick="showListenedFunc('*');" class="btn btn-sm btn-outline-white">查看全部会话监听</a>
                      </div>
                    </div>
                    <div class="row g-2 mb-3">
                      <textarea id="listened-text" class="form-control border-top" disabled="disabled" style="height: 30vh; border-top: none;"></textarea>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <footer class="footer footer-transparent d-print-none">
          <div class="container-xl" style="width: 98%;">
            <div class="row text-center align-items-center flex-row-reverse">
              <div class="col-lg-auto ms-lg-auto">
                <ul class="list-inline list-inline-dots mb-0">
                  <li class="list-inline-item"><a href="https://github.com/tdragon6/Supershell/blob/main/LICENSE" target="_blank" class="link-secondary" style="text-decoration:none;">License</a></li>
                  <li class="list-inline-item"><a href="https://github.com/tdragon6/Supershell" target="_blank" class="link-secondary" style="text-decoration:none;">Github</a></li>
                  <li class="list-inline-item">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon text-pink icon-filled icon-inline" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M19.5 12.572l-7.5 7.428l-7.5 -7.428m0 0a5 5 0 1 1 7.5 -6.566a5 5 0 1 1 7.5 6.572" /></svg>
                      93dc2d41ae035a65
                  </li>
                </ul>
              </div>
              <div class="col-12 col-lg-auto mt-3 mt-lg-0">
                <ul class="list-inline list-inline-dots mb-0">
                  <li class="list-inline-item">
                    Copyright &copy; {{ year }} Black. All rights reserved.
                  </li>
                  <li class="list-inline-item">
                    <a>{{ black_version }}</a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </footer>
      </div>
     </div>
    <script src="/static/js/tabler.min.js"></script>
    <script src="/static/js/jquery.min.js"></script>
    <script src="/static/js/juqery.cookie.min.js"></script>
    <script src="/static/js/toastr.min.js"></script>
    <script src="/static/js/func/session.js"></script>
    <script src="/static/js/func/session_advanced.js"></script>
    <script>
        // 设置提示框属性
        toastr.options.positionClass = 'toast-top-center';
        toastr.options.timeOut = 3000;
        toastr.options.escapeHtml = true;
    </script>
    <script>
        // 获取sessid
        var sessid = '{{ sessid | safe }}';

        // 设置菜单栏指向的链接
        set_menu_link('{{ sessid | safe }}');
    </script>
  </body>
</html>