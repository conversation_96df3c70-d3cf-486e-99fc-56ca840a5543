limit_req_zone $binary_remote_addr zone=one:10m rate=20r/m;

map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}

server {
    listen 80;

    # black web服务静态资源托管
    location /static {
        alias /static;
    }

    # black web服务映射
    location / {
        proxy_pass http://flask:5000/;
    }

    # black webhook服务映射，限制只能通过本地访问
    location /black/webhook {
        proxy_pass http://flask:5000/black/webhook;
        deny 8.8.8.8;
        allow 127.0.0.1;
        deny all;
    }

    # black web服务生成客户端Payload映射
    location = /black/compile/make {
        proxy_read_timeout 9999999;
        proxy_pass http://flask:5000/black/compile/make;
    }

    # black web服务常用文件文件流上传映射
    location = /black/server/files/upload {
        proxy_read_timeout 9999999;
        proxy_request_buffering off;
        lingering_close off;
        proxy_pass http://flask:5000/black/server/files/upload;
    }

    # black web服务从常用文件上传至目标目录文件流上传映射
    location = /black/session/files/upload/server {
        proxy_read_timeout 9999999;
        proxy_pass http://flask:5000/black/session/files/upload/server;
    }

    # black web服务从本地文件上传至目标目录文件流上传映射
    location = /black/session/files/upload/local {
        proxy_read_timeout 9999999;
        proxy_request_buffering off;
        lingering_close off;
        proxy_pass http://flask:5000/black/session/files/upload/local;
    }

    # black web服务登录鉴权接口映射，设置请求频率
    location = /black/login/auth {
        proxy_pass http://flask:5000/black/login/auth;
        limit_req zone=one burst=2;
    }

    # black 共享shell登录鉴权接口映射，设置请求频率
    location = /black/share/shell/login/auth {
        proxy_pass http://flask:5000/black/share/shell/login/auth;
        limit_req zone=one burst=2;
    }

    # ttyd shell映射和鉴权
    location = /black/shell {
        proxy_pass http://shell:7681/;
        set $auth_uri http://flask:5000/black/share/shell/auth?arg=$arg_arg;
        set $share_uri http://flask:5000/black/share/shell/login?arg=$arg_arg;
        set $void_uri http://flask:5000/black/session/nginx/void?arg=$arg_arg;
        auth_request /black/share/shell/auth;
        error_page 401 /black/share/shell/login;
        error_page 403 /black/session/nginx/void;
    }

    # ttyd shell token映射
    location /black/shell/token {
        proxy_pass http://shell:7681/token;
    }

    # ttyd shell websocket映射
    location /black/shell/ws {
        proxy_pass http://shell:7681/ws;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # 鉴权URL，带参数交给flask处理
    location = /black/share/shell/auth {
        proxy_pass $auth_uri;
    }

    # 鉴权失败URL，带参数交给flask处理
    location = /black/share/shell/login {
        proxy_pass $share_uri;
    }

    # 鉴权成功但找不到session URL，带参数交给flask处理
    location = /black/session/nginx/void {
        proxy_pass $void_uri;
    }

    # ttyd inject映射和鉴权
    location = /black/memfd/inject {
        proxy_pass http://memfd:7682/;
        auth_request /black/memfd/inject/auth;
    }

    # ttyd inject websocket映射
    location /black/memfd/inject/ws {
        proxy_pass http://memfd:7682/ws;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}