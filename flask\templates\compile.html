<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <title>Black - 客户端生成</title>
    <link rel="icon" href="/static/img/favicon.ico" type="image/x-icon">
    <link href="/static/css/tabler.min.css" rel="stylesheet"/>
    <link href="/static/css/tabler-vendors.min.css" rel="stylesheet"/>
    <link href="/static/css/toastr.min.css" rel="stylesheet"/>
    <style>
      @import url('https://rsms.me/inter/inter.css');
      :root {
      	--tblr-font-sans-serif: 'Inter Var', -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;
      }
      body {
      	font-feature-settings: "cv03", "cv04", "cv11";
      }
    </style>
  </head>
  <body class="layout-fluid theme-light">
    <script src="/static/js/demo-theme.min.js"></script>
    <div class="modal modal-blur show" id="make-progress" tabindex="-1" aria-modal="true" role="dialog" style="display: none;">
      <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
        <div class="modal-content">
          <div class="modal-body">
            <div class="modal-title">正在生成 <strong id="make-progress-filename"></strong></div>
              <div class="mb-4">正在后台生成客户端Payload，每种系统架构第一次生成大约需要1分钟多，关闭此进度框或关闭页面不会中断，生成完毕后会在已生成客户端列表中显示。<br><br><strong style="color: red">同一时间请生成一个客户端Payload，尽量不要并发。</strong></div>
            <div class="progress">
                <div class="progress-bar progress-bar-indeterminate bg-blue"></div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-link link-secondary" data-bs-dismiss="modal" style="text-decoration: none;">关闭</button>
          </div>
        </div>
      </div>
    </div>
    <div class="modal modal-blur show" id="delete" tabindex="-1" aria-modal="true" role="dialog" style="display: none;">
      <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
        <div class="modal-content">
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          <div class="modal-status bg-danger"></div>
          <div class="modal-body text-center py-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="icon mb-2 text-danger icon-lg" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M12 9v2m0 4v.01"></path><path d="M5 19h14a2 2 0 0 0 1.84 -2.75l-7.1 -12.25a2 2 0 0 0 -3.5 0l-7.1 12.25a2 2 0 0 0 1.75 2.75"></path></svg>
            <h3>确定要删除 <strong id="delete_file_name"></strong> 吗？</h3>
          </div>
          <div class="modal-footer">
            <div id="delete-loader" class="spinner-border spinner-border-sm text-muted" role="status"></div>
            <a id="delete-btn" onclick="deleteCompileFile($('#delete_file_name').text());" href="javascript:void(0);" class="btn btn-danger w-100">确认</a>
          </div>
        </div>
      </div>
    </div>
    <div class="page">
      <aside class="navbar navbar-vertical navbar-expand-lg navbar-dark">
        <div class="container-fluid">
          <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#sidebar-menu" aria-controls="sidebar-menu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
          </button>
          <h1 class="navbar-brand navbar-brand-autodark">
            <a href="/black/monitor">
              <img src="/static/img/logo-white.svg" width="110" height="32" alt="Black" class="navbar-brand-image">
            </a>
          </h1>
          <div class="collapse navbar-collapse" id="sidebar-menu">
            <ul class="navbar-nav pt-lg-3">
              <li class="nav-item">
                <a class="nav-link" href="/black/monitor">
                  <span class="nav-link-icon d-md-none d-lg-inline-block">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-home" width="32" height="32" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                      <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                      <path d="M5 12l-2 0l9 -9l9 9l-2 0"></path>
                      <path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7"></path>
                      <path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6"></path>
                   </svg>
                  </span>
                  <span class="nav-link-title" style="font-size: 15px">
                    监控台
                  </span>
                </a>
              </li>
              <li class="nav-item dropdown active">
                <a class="nav-link dropdown-toggle" href="javascript:void(0)" data-bs-toggle="dropdown" data-bs-auto-close="false" role="button" aria-expanded="false" >
                  <span class="nav-link-icon d-md-none d-lg-inline-block">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-device-desktop" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                      <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                      <path d="M3 4m0 1a1 1 0 0 1 1 -1h16a1 1 0 0 1 1 1v10a1 1 0 0 1 -1 1h-16a1 1 0 0 1 -1 -1z"></path>
                      <path d="M7 20l10 0"></path>
                      <path d="M9 16l0 4"></path>
                      <path d="M15 16l0 4"></path>
                   </svg>
                  </span>
                  <span class="nav-link-title" style="font-size: 15px">
                    客户端
                  </span>
                </a>
                <div class="dropdown-menu show">
                  <div class="dropdown-menu-columns">
                    <div class="dropdown-menu-column">
                      <a class="dropdown-item" href="/black/client">
                        客户端列表
                      </a>
                      <a class="dropdown-item active" href="/black/compile">
                        客户端生成
                      </a>
                    </div>
                  </div>
                </div>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="/black/server/files" >
                  <span class="nav-link-icon d-md-none d-lg-inline-block">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-files" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                       <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                       <path d="M15 3v4a1 1 0 0 0 1 1h4"></path>
                       <path d="M18 17h-7a2 2 0 0 1 -2 -2v-10a2 2 0 0 1 2 -2h4l5 5v7a2 2 0 0 1 -2 2z"></path>
                       <path d="M16 17v2a2 2 0 0 1 -2 2h-7a2 2 0 0 1 -2 -2v-10a2 2 0 0 1 2 -2h2"></path>
                    </svg>
                  </span>
                  <span class="nav-link-title" style="font-size: 15px">
                    常用文件
                  </span>
                </a>
              </li>
              <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="javascript:void(0)" data-bs-toggle="dropdown" data-bs-auto-close="false" role="button" aria-expanded="false" >
                  <span class="nav-link-icon d-md-none d-lg-inline-block">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-article" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                       <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                       <path d="M3 4m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z"></path>
                       <path d="M7 8h10"></path>
                       <path d="M7 12h10"></path>
                       <path d="M7 16h10"></path>
                    </svg>
                  </span>
                  <span class="nav-link-title" style="font-size: 15px">
                    日志监测
                  </span>
                </a>
                <div class="dropdown-menu show">
                  <div class="dropdown-menu-columns">
                    <div class="dropdown-menu-column">
                      <a class="dropdown-item" href="/black/log/flask">
                        业务日志
                      </a>
                      <a class="dropdown-item" href="/black/log/gunicorn">
                        Gunicorn日志
                      </a>
                    </div>
                  </div>
                </div>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="/black/notes" >
                  <span class="nav-link-icon d-md-none d-lg-inline-block">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-pencil" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                      <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                      <path d="M4 20h4l10.5 -10.5a1.5 1.5 0 0 0 -4 -4l-10.5 10.5v4"></path>
                      <path d="M13.5 6.5l4 4"></path>
                    </svg>
                  </span>
                  <span class="nav-link-title" style="font-size: 15px">
                    备忘录
                  </span>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="/black/setting" >
                  <span class="nav-link-icon d-md-none d-lg-inline-block">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-settings" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                      <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                      <path d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z"></path>
                      <path d="M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"></path>
                    </svg>
                  </span>
                  <span class="nav-link-title" style="font-size: 15px">
                    设置
                  </span>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </aside>
      <header class="navbar navbar-expand-md navbar-light d-none d-lg-flex d-print-none">
        <div class="container-xl">
          <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu" aria-controls="navbar-menu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
          </button>
          <div class="navbar-nav flex-row order-md-last">
            <div class="d-none d-md-flex">
              <a href="?theme=dark" class="nav-link px-0 hide-theme-dark" title="暗黑模式" data-bs-toggle="tooltip"
		   data-bs-placement="bottom">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" /></svg>
              </a>
              <a href="?theme=light" class="nav-link px-0 hide-theme-light" title="明亮模式" data-bs-toggle="tooltip"
		   data-bs-placement="bottom">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="12" cy="12" r="4" /><path d="M3 12h1m8 -9v1m8 8h1m-9 8v1m-6.4 -15.4l.7 .7m12.1 -.7l-.7 .7m0 11.4l.7 .7m-12.1 -.7l-.7 .7" /></svg>
              </a>
            </div>
          </div>
          <div class="collapse navbar-collapse" id="navbar-menu">
            <div class="nav-item dropdown">
              <a href="javascript:void(0)" class="nav-link d-flex lh-1 text-reset p-0" data-bs-toggle="dropdown">
                <span class="avatar avatar-sm" style="background-image: url(/static/img/avatar.jpg)"></span>
                <div class="d-none d-xl-block ps-2">
                  <div>{{ username }}</div>
                  <div class="mt-1 small text-muted">Hacker</div>
                </div>
              </a>
              <div class="dropdown-menu dropdown-menu-arrow">
                <a href="/black/setting" class="dropdown-item">
                  <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-settings" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z"></path>
                    <path d="M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"></path>
                  </svg>&nbsp;
                  设置
                </a>
                <a href="https://github.com/tdragon6/Black" target="_blank" class="dropdown-item">
                  <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-brand-github" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M9 19c-4.3 1.4 -4.3 -2.5 -6 -3m12 5v-3.5c0 -1 .1 -1.4 -.5 -2c2.8 -.3 5.5 -1.4 5.5 -6a4.6 4.6 0 0 0 -1.3 -3.2a4.2 4.2 0 0 0 -.1 -3.2s-1.1 -.3 -3.5 1.3a12.3 12.3 0 0 0 -6.2 0c-2.4 -1.6 -3.5 -1.3 -3.5 -1.3a4.2 4.2 0 0 0 -.1 3.2a4.6 4.6 0 0 0 -1.3 3.2c0 4.6 2.7 5.7 5.5 6c-.6 .6 -.6 1.2 -.5 2v3.5"></path>
                  </svg>&nbsp;
                  Github
                </a>
                <div class="dropdown-divider"></div>
                <a href="javascript:void(0);" onclick="$.removeCookie('token',{ path: '/'});location.reload();" class="dropdown-item">
                  <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-logout" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M14 8v-2a2 2 0 0 0 -2 -2h-7a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2 -2v-2"></path>
                    <path d="M7 12h14l-3 -3m0 6l3 -3"></path>
                  </svg>&nbsp;
                  注销
                </a>
              </div>
            </div>
          </div>
        </div>
      </header>
      <div class="page-wrapper">
        <div class="page-body">
          <div class="container-xl">
            <div class="row row-deck row-cards">
              <div class="col-12">
                <div class="card">
                  <div class="card-header">
                    <span id="make-loader"></span>
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <h3 class="card-title">客户端生成</h3>
                  </div>
                  <div class="card-body row">
                    <div class="col-4">
                      <div class="mb-3 row">
                        <label class="col-3 col-form-label"><font color="#FF0000">*</font> 文件名:</label>
                        <div class="col">
                          <input id="make-compile-filename" type="text" class="form-control" placeholder="客户端Payload文件名" style="width: 90%;">
                        </div>
                      </div>
                      <div class="mb-3 row">
                        <label class="col-3 col-form-label"><font color="#FF0000">*</font> 回连地址:</label>
                        <div class="col">
                          <input id="make-compile-address" type="text" class="form-control" placeholder="VPS公网地址" style="width: 90%;">
                        </div>
                      </div>
                      <div class="mb-3 row">
                        <label class="col-3 col-form-label"><font color="#FF0000">*</font> 回连端口:</label>
                        <div class="col">
                          <input id="make-compile-port" type="text" class="form-control" placeholder="RSSH公网端口，默认3232" style="width: 90%;">
                        </div>
                      </div>
                      <div class="mb-3 row">
                        <label class="col-3 col-form-label"><font color="#FF0000">*</font> 进程名:</label>
                        <div class="col">
                          <input id="make-compile-process" type="text" class="form-control" placeholder="必填,_代替空格,*代替-" style="width: 90%;">
                        </div>
                      </div>
                    </div>
                    <div class="col-4">
                      <div class="mb-3 row">
                        <label class="col-3 col-form-label"><font color="#FF0000">*</font> 系统架构:</label>
                        <div class="col">
                          <select type="text" class="form-select" placeholder="选择系统架构" id="select-tags" value="" style="width: 90%;">
                          </select>
                        </div>
                      </div>
                      <div class="mb-3 row">
                        <label class="col-3 col-form-label">代理地址:</label>
                        <div class="col">
                          <input id="make-compile-address-proxy" type="text" class="form-control" placeholder="代理地址，如127.0.0.1" style="width: 90%;">
                        </div>
                      </div>
                      <div class="mb-3 row">
                        <label class="col-3 col-form-label">代理端口:</label>
                        <div class="col">
                          <input id="make-compile-port-proxy" type="text" class="form-control" placeholder="代理端口，如7890" style="width: 90%;">
                        </div>
                      </div>
                      <div class="mb-3 row">
                        <label class="col-3 col-form-label"><font color="#FF0000">*</font> 日志等级:</label>
                        <div class="col">
                          <select type="text" class="form-select" placeholder="选择日志等级" id="select-logs" value="" style="width: 90%;">
                          </select>
                        </div>
                      </div>
                    </div>
                    <div class="col-4">
                      <div class="mb-4 row">
                        <label class="col-3 col-form-label"><font color="#FF0000">*</font> 流量类型:</label>
                        <div class="col">
                          <select type="text" class="form-select" placeholder="选择流量封装类型" id="select-tags-flow" value="" style="width: 90%;">
                          </select>
                        </div>
                      </div>
                      <div class="mb-3 row">
                        <div class="col-8">
                          <div class="mb-3">
                            <label class="form-check form-check-inline">
                              <input id="make-compile-upx" class="form-check-input" type="checkbox" checked>
                              <span class="form-check-label">压缩</span>
                            </label>
                            <span class="form-help" data-bs-toggle="popover" data-bs-placement="top" data-bs-html="true" data-bs-content="使用Upx加壳压缩，缩小客户端Payload文件大小，并起到一定的免杀效果，部分系统架构不支持。">?</span>
                          </div>
                          <div class="mb-3">
                            <label class="form-check form-check-inline">
                              <input id="make-compile-garble" class="form-check-input" type="checkbox" checked>
                              <span class="form-check-label">免杀</span>
                            </label>
                            <span class="form-help" data-bs-toggle="popover" data-bs-placement="top" data-bs-html="true" data-bs-content="使用Garble混淆免杀，起到一定的免杀效果。注意：并不是所有情况免杀效果都会提升，有时甚至还会减弱免杀效果，具体以目标主机测试为准。">?</span>
                          </div>
                        </div>
                        <div class="col-4" style="margin-top: 30px">
                          <a href="javascript:void(0);" onclick="make_client_payload();" class="btn btn-primary">生成</a>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-12">
                <div class="card">
                  <div class="card-header">
                    <span id="compiled-loader"></span>
                    &nbsp;&nbsp;
                    <h3 class="card-title">已生成客户端</h3>
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <a class="nav-link" href="javascript:void(0)" onclick="update_compiled_client(search_text);" title="刷新" data-bs-toggle="tooltip" data-bs-placement="top">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-refresh" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"></path>
                        <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"></path>
                      </svg>
                    </a>
                    <div class="ms-auto text-muted input-icon">
                      <input id="search" type="text" class="form-control form-control-sm" placeholder="搜索">
                      <label class="input-icon-addon">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="20" height="20" viewBox="0 0 28 28" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path><path d="M21 21l-6 -6"></path></svg>
                      </label>
                    </div>
                  </div>
                  <div class="table-responsive" style="overflow: visible;">
                    <table class="table table-hover card-table table-vcenter">
                      <thead>
                        <tr>
                          <th style="font-size: 14px;">#</th>
                          <th style="font-size: 14px;">文件名</th>
                          <th style="font-size: 14px;">回连地址</th>
                          <th style="font-size: 14px;">系统</th>
                          <th style="font-size: 14px;">架构</th>
                          <th style="font-size: 14px;">文件类型</th>
                          <th style="font-size: 14px;">文件大小</th>
                          <th style="font-size: 14px;">版本</th>
                          <th style="font-size: 14px;">生成时间</th>
                          <th style="font-size: 14px;">操作</th>
                        </tr>
                      </thead>
                      <tbody id="compiled_table">
                      </tbody>
                    </table>
                  </div>
                  <div class="card-footer d-flex align-items-center">
                    <div class="text-secondary">
                      设置
                      <div class="mx-2 d-inline-block">
                        <input id="page-change-num" type="text" class="form-control form-control-sm" size="3">
                      </div>
                      条/页
                    </div>
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <p id="pages-info" class="m-0 text-muted col-3">无数据</p>
                    <ul id="pages-button" class="pagination m-0 ms-auto" style="overflow: overlay;"></ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <footer class="footer footer-transparent d-print-none">
          <div class="container-xl" style="width: 98%;">
            <div class="row text-center align-items-center flex-row-reverse">
              <div class="col-lg-auto ms-lg-auto">
                <ul class="list-inline list-inline-dots mb-0">
                  <li class="list-inline-item"><a href="https://github.com/tdragon6/Black/blob/main/LICENSE" target="_blank" class="link-secondary" style="text-decoration:none;">License</a></li>
                  <li class="list-inline-item"><a href="https://github.com/tdragon6/Black" target="_blank" class="link-secondary" style="text-decoration:none;">Github</a></li>
                  <li class="list-inline-item">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon text-pink icon-filled icon-inline" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M19.5 12.572l-7.5 7.428l-7.5 -7.428m0 0a5 5 0 1 1 7.5 -6.566a5 5 0 1 1 7.5 6.572" /></svg>
                      93dc2d41ae035a65
                  </li>
                </ul>
              </div>
              <div class="col-12 col-lg-auto mt-3 mt-lg-0">
                <ul class="list-inline list-inline-dots mb-0">
                  <li class="list-inline-item">
                    Copyright &copy; {{ year }} Black. All rights reserved.
                  </li>
                  <li class="list-inline-item">
                    <a>{{ black_version }}</a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
    <script src="/static/js/tom-select.base.min.js"></script>
    <script src="/static/js/tabler.min.js"></script>
    <script src="/static/js/jquery.min.js"></script>
    <script src="/static/js/juqery.cookie.min.js"></script>
    <script src="/static/js/clipboard.js"></script>
    <script src="/static/js/toastr.min.js"></script>
    <script src="/static/js/func/compile.js"></script>
    <script src="/static/js/func/func.js"></script>
    <script>
      // 设置提示框属性
      toastr.options.positionClass = 'toast-top-center';
      toastr.options.timeOut = 3000;
      toastr.options.escapeHtml = true;
    </script>
    <script>
      var search_text = ''; // 搜索文本
      
      // 原始已生成客户端数据分组步长
      var pages_size = 5;
      if ($.cookie('compile_page_num') !== undefined){
        if (check_positive_integer($.cookie('compile_page_num'))){
          pages_size = Number($.cookie('compile_page_num'));
        }
      }
      $('#page-change-num').val(pages_size.toString());

      var clients_list = []; // 原始列表类型已生成客户端数据

      // 获取客户端支持生成的操作系统架构和流量伪装类型，显示到select框
      get_select();

      // 获取已生成的客户端列表
      update_compiled_client(search_text);

      //回车提交每页条数配置
      page_enter_listen();

      // 回车提交搜索
      search_enter_listen();
    </script>
  </body>
</html>