services:                                                                                                                                                                                                                                                                      
  container1:
    image: archlinux
    command: sleep infinity
    volumes:
       - ./bin/:/usr/local/bin
    networks:
      custom_network:
        ipv4_address: **********


  container2:
    image: archlinux
    ports:
      - "3232:3232/tcp"
    volumes:
       - ./bin/:/usr/local/bin
    command: sleep infinity
    networks:
      custom_network:
        ipv4_address: **********

  container3:
    image: archlinux
    command: sleep infinity
    privileged: true
    networks:
      custom_network:
        ipv4_address: **********

networks:
  custom_network:
    ipam:
      config:
        - subnet: **********/24
          gateway: **********