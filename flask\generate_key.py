'''
    生成rssh连接的公私钥对
'''

from const import key_file, public_key_file, log_path
import paramiko
import logging
import os


logging.basicConfig(level=logging.INFO,
                    filename=log_path + '/flask.log',
                    filemode='a',
                    format='%(asctime)s - %(levelname)s - %(filename)s - %(funcName)s - %(lineno)s - %(message)s'
                    )


def generateSSHKey(private_filepath, public_filepath):
    '''
        生成rssh连接的公私钥对
    '''
    # 确保目录存在
    os.makedirs(os.path.dirname(private_filepath), exist_ok=True)

    # 如果公钥路径是目录，删除它
    if os.path.isdir(public_filepath):
        import shutil
        shutil.rmtree(public_filepath)
        logging.info('Removed directory at public key path: ' + public_filepath)

    # 如果私钥路径是目录，删除它
    if os.path.isdir(private_filepath):
        import shutil
        shutil.rmtree(private_filepath)
        logging.info('Removed directory at private key path: ' + private_filepath)

    if os.path.exists(private_filepath) and os.path.exists(public_filepath):
        logging.info('SSH Key Existed, Skip Generate Key.')
        return

    # 生成RSA密钥对
    key = paramiko.RSAKey.generate(2048)

    # 写入私钥文件
    key.write_private_key_file(private_filepath)

    # 写入公钥文件，使用标准SSH公钥格式
    with open(public_filepath, "w") as public:
        public.write("%s %s black@flask\n" % (key.get_name(), key.get_base64()))

    # 设置正确的文件权限
    os.chmod(private_filepath, 0o600)
    os.chmod(public_filepath, 0o644)

    logging.info('New SSH Key Generated.')


if __name__ == "__main__":
    generateSSHKey(key_file, public_key_file)