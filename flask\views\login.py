'''
    登录界面蓝图
'''

from flask import Blueprint, request, redirect, render_template, make_response, jsonify
from config import user, pwd, global_salt, expire
from const import black_version_dict
from module.func import get_jwt_token
import jwt
import hashlib


login_view = Blueprint('login', __name__)


@login_view.route('/black/login', methods=['GET'])
def login():
    '''
        登陆页面功能
    '''
    if request.method == 'GET':
        try:
            token = request.cookies.get('token')
            result = jwt.decode(token, global_salt, algorithms=['HS256'])
            if result['username'] == user:
                return redirect('/black/monitor')
            else:
                return render_template('login.html', black_version=black_version_dict['version'])
        except:
            return render_template('login.html', black_version=black_version_dict['version'])


@login_view.route('/black/login/auth', methods=['POST'])
def login_auth():
    '''
        登录鉴权点
    '''
    if request.method == 'POST':
        data = request.json
        username = data.get('username')
        password = data.get('password')
        if username == user and hashlib.md5(password.encode('utf-8')).hexdigest() == pwd:
            response = make_response(jsonify({'result': 'success'}))
            jwt_token = get_jwt_token(username, global_salt, expire * 60 * 60)
            response.set_cookie('token', jwt_token)
            return response
        else:
            return jsonify({'result': 'failed'})