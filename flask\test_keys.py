#!/usr/bin/env python3
'''
    测试SSH密钥是否正确生成
'''

import os
import paramiko
from const import key_file, public_key_file

def test_keys():
    print("Testing SSH keys...")
    
    # 检查文件是否存在
    print(f"Private key file exists: {os.path.exists(key_file)}")
    print(f"Public key file exists: {os.path.exists(public_key_file)}")
    
    if os.path.exists(key_file):
        try:
            # 尝试加载私钥
            private_key = paramiko.RSAKey.from_private_key_file(key_file)
            print("✅ Private key loaded successfully")
            
            # 获取公钥指纹
            public_key = private_key.get_base64()
            print(f"Public key fingerprint: {public_key[:50]}...")
            
        except Exception as e:
            print(f"❌ Failed to load private key: {e}")
    
    if os.path.exists(public_key_file):
        try:
            # 读取公钥文件内容
            with open(public_key_file, 'r') as f:
                content = f.read()
            print(f"Public key file content: {content.strip()}")
            
            # 尝试解析公钥
            import base64
            parts = content.strip().split()
            if len(parts) >= 2:
                key_data = base64.b64decode(parts[1])
                print("✅ Public key format appears correct")
            else:
                print("❌ Public key format appears incorrect")
                
        except Exception as e:
            print(f"❌ Failed to read public key: {e}")

if __name__ == "__main__":
    test_keys()
