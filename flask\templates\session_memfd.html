<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <title>Black - 内存执行</title>
    <link rel="icon" href="/static/img/favicon.ico" type="image/x-icon">
    <link href="/static/css/tabler.min.css" rel="stylesheet"/>
    <link href="/static/css/toastr.min.css" rel="stylesheet"/>
    <style>
      @import url('https://rsms.me/inter/inter.css');
      :root {
      	--tblr-font-sans-serif: 'Inter Var', -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;
      }
      body {
      	font-feature-settings: "cv03", "cv04", "cv11";
      }
    </style>
  </head>
  <body class="layout-fluid">
    <script src="/static/js/demo-theme.min.js"></script>
    <div class="modal modal-blur show" id="local_inject" tabindex="-1" role="dialog" aria-modal="true" style="display: none;">
      <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">本地注入</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="card">
            <div class="card-header">
              <span id="serverFiles-loader"></span>
              &nbsp;&nbsp;
              <h3 class="card-title">常用文件列表</h3>
              &nbsp;&nbsp;&nbsp;&nbsp;
              <a class="nav-link" href="javascript:void(0)" onclick="get_server_files_list();" title="刷新" data-bs-toggle="tooltip" data-bs-placement="top">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-refresh" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                  <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                  <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"></path>
                  <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"></path>
                </svg>
              </a>
              &nbsp;&nbsp;&nbsp;&nbsp;
              <a class="nav-link ms-auto" href="/black/server/files" target="_blank" title="进入常用文件管理" data-bs-toggle="tooltip" data-bs-placement="top">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-files" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                   <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                   <path d="M15 3v4a1 1 0 0 0 1 1h4"></path>
                   <path d="M18 17h-7a2 2 0 0 1 -2 -2v-10a2 2 0 0 1 2 -2h4l5 5v7a2 2 0 0 1 -2 2z"></path>
                   <path d="M16 17v2a2 2 0 0 1 -2 2h-7a2 2 0 0 1 -2 -2v-10a2 2 0 0 1 2 -2h2"></path>
                </svg>
              </a>
            </div>
            <div class="table-responsive" style="overflow: visible;">
              <table class="table table-hover card-table table-vcenter">
              <thead>
                <tr>
                  <th style="font-size: 14px;">#</th>
                  <th style="font-size: 14px;">
                    <button id="field_server_file_name" class="table-sort" onclick="sort_by_field_server_files($(this).attr('id'), true)">文件名</button>
                  </th>
                  <th style="font-size: 14px;">
                    <button id="field_server_file_size" class="table-sort" onclick="sort_by_field_server_files($(this).attr('id'), true)">文件大小</button>
                  </th>
                  <th style="font-size: 14px;">
                    <button id="field_server_file_time" class="table-sort" onclick="sort_by_field_server_files($(this).attr('id'), true)">修改时间</button>
                  </th>
                  <th style="font-size: 14px;">操作</th>
                </tr>
              </thead>
              <tbody id="server_files_table">
              </tbody>
            </table>
            </div>
            <div class="card-footer d-flex align-items-center">
              <p id="serverFilesPages-info" class="m-0 text-muted col-4">无数据</p>
              <ul id="serverFilesPages-button" class="pagination m-0 ms-auto" style="overflow: overlay;"></ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="page">
      <header class="navbar navbar-expand-md navbar-light d-print-none">
        <div class="container-xl">
          <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu" aria-controls="navbar-menu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
          </button>
          <h1 class="navbar-brand navbar-brand-autodark d-none-navbar-horizontal pe-0 pe-md-3">
            <a href="/black/monitor">
              <img src="/static/img/logo.svg" width="110" height="32" alt="Black" class="navbar-brand-image">
            </a>
          </h1>
          <div class="navbar-nav flex-row order-md-last">
            <div class="d-none d-md-flex me-3">
              <a href="?arg={{ sessid | safe }}&theme=dark" class="nav-link px-0 hide-theme-dark" title="暗黑模式" data-bs-toggle="tooltip"
		   data-bs-placement="bottom">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" /></svg>
              </a>
              <a href="?arg={{ sessid | safe }}&theme=light" class="nav-link px-0 hide-theme-light" title="明亮模式" data-bs-toggle="tooltip"
		   data-bs-placement="bottom">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="12" cy="12" r="4" /><path d="M3 12h1m8 -9v1m8 8h1m-9 8v1m-6.4 -15.4l.7 .7m12.1 -.7l-.7 .7m0 11.4l.7 .7m-12.1 -.7l-.7 .7" /></svg>
              </a>
            </div>
            <div class="nav-item dropdown">
              <a href="javascript:void(0)" class="nav-link d-flex lh-1 text-reset p-0" data-bs-toggle="dropdown">
                <span class="avatar avatar-sm" style="background-image: url(/static/img/avatar.jpg)"></span>
                <div class="d-none d-xl-block ps-2">
                  <div>{{ username }}</div>
                  <div class="mt-1 small text-muted">Hacker</div>
                </div>
              </a>
              <div class="dropdown-menu dropdown-menu-arrow">
                <a href="/black/setting" target="_blank" class="dropdown-item">
                  <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-settings" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z"></path>
                    <path d="M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"></path>
                  </svg>&nbsp;
                  设置
                </a>
                <a href="https://github.com/tdragon6/Black" target="_blank" class="dropdown-item">
                  <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-brand-github" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M9 19c-4.3 1.4 -4.3 -2.5 -6 -3m12 5v-3.5c0 -1 .1 -1.4 -.5 -2c2.8 -.3 5.5 -1.4 5.5 -6a4.6 4.6 0 0 0 -1.3 -3.2a4.2 4.2 0 0 0 -.1 -3.2s-1.1 -.3 -3.5 1.3a12.3 12.3 0 0 0 -6.2 0c-2.4 -1.6 -3.5 -1.3 -3.5 -1.3a4.2 4.2 0 0 0 -.1 3.2a4.6 4.6 0 0 0 -1.3 3.2c0 4.6 2.7 5.7 5.5 6c-.6 .6 -.6 1.2 -.5 2v3.5"></path>
                  </svg>&nbsp;
                  Github
                </a>
                <div class="dropdown-divider"></div>
                <a href="javascript:void(0);" onclick="$.removeCookie('token',{ path: '/'});location.reload();" class="dropdown-item">
                  <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-logout" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M14 8v-2a2 2 0 0 0 -2 -2h-7a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2 -2v-2"></path>
                    <path d="M7 12h14l-3 -3m0 6l3 -3"></path>
                  </svg>&nbsp;
                  注销
                </a>
              </div>
            </div>
          </div>
        </div>
      </header>
      <header class="navbar-expand-md">
        <div class="collapse navbar-collapse" id="navbar-menu">
          <div class="navbar navbar-light">
            <div class="container-xl">
              <ul class="navbar-nav">
                <li class="nav-item">
                  <a class="nav-link" href="" id="session-info">
                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-info-square-rounded" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M12 8h.01"></path>
                        <path d="M11 12h1v4h1"></path>
                        <path d="M12 3c7.2 0 9 1.8 9 9s-1.8 9 -9 9s-9 -1.8 -9 -9s1.8 -9 9 -9z"></path>
                      </svg>
                    </span>
                    <span class="nav-link-title">
                      会话信息
                    </span>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" href="" id="session-shell">
                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-brand-tabler" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                         <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                         <path d="M8 9l3 3l-3 3"></path>
                         <path d="M13 15l3 0"></path>
                         <path d="M4 4m0 4a4 4 0 0 1 4 -4h8a4 4 0 0 1 4 4v8a4 4 0 0 1 -4 4h-8a4 4 0 0 1 -4 -4z"></path>
                      </svg>
                    </span>
                    <span class="nav-link-title">
                      交互终端
                    </span>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" href="" id="session-files">
                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-file-description" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M14 3v4a1 1 0 0 0 1 1h4"></path>
                        <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"></path>
                        <path d="M9 17h6"></path>
                        <path d="M9 13h6"></path>
                      </svg>
                    </span>
                    <span class="nav-link-title">
                      文件管理
                    </span>
                  </a>
                </li>
                <li class="nav-item active">
                  <a class="nav-link" href="" id="session-memfd">
                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-file-x" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                         <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                         <path d="M14 3v4a1 1 0 0 0 1 1h4"></path>
                         <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"></path>
                       <path d="M10 12l4 4m0 -4l-4 4"></path>
                      </svg>
                    </span>
                    <span class="nav-link-title">
                      内存执行
                    </span>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" href="" id="session-advanced">
                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-swords" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                         <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                         <path d="M21 3v5l-11 9l-4 4l-3 -3l4 -4l9 -11z"></path>
                         <path d="M5 13l6 6"></path>
                         <path d="M14.32 17.32l3.68 3.68l3 -3l-3.365 -3.365"></path>
                         <path d="M10 5.5l-2 -2.5h-5v5l3 2.5"></path>
                      </svg>
                    </span>
                    <span class="nav-link-title">
                      进阶功能
                    </span>
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </header>
      <div class="page-wrapper">
        <div class="page-body">
          <div class="container-xl">
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">内存执行</h3>
              </div>
              <div class="card-body">
                <h3 class="card-title">说明</h3>
                <p>将可执行文件注入内存，实现无文件落地运行Payload。不只是linux的内存注入，windows等同样支持！
                    <br>支持远程注入和本地注入两种方式，远程注入接收可执行文件下载链接，本地注入可将常用文件服务器的文件注入至目标主机内存。
                    <br>无论哪种方式都会将文件数据传输至目标主机，因此不要注入过大文件。手工判断注入成功后关闭此页面可释放与目标的注入通道，但在文件数据传输完前（注入成功前）关闭此页面会中断注入。</p>
                <div class="row">
                    <div class="col-6">
                        <h3 class="card-title mt-2">远程注入</h3>
                        <div>
                          <div class="row g-3">
                            <div class="input-icon col-10">
                              <input id="remote_url" type="text" class="form-control" placeholder="远程可执行文件URL下载链接">
                            </div>
                            <div class="col-auto">
                                <a href="javascript:void(0);" onclick="inject_remote_memfd(sessid);" class="btn btn-primary">远程注入</a>
                            </div>
                          </div>
                        </div>
                    </div>
                    <div class="col-2"></div>
                    <div class="col-4">
                        <h3 class="card-title mt-2">本地注入</h3>
                        <a href="javascript:void(0);" onclick="show_localInject();" class="btn btn-primary mb-3">打开列表</a>
                    </div>
                </div>
              </div>
              <iframe id="inject-shell" src=""></iframe>
            </div>
          </div>
        </div>
        <footer class="footer footer-transparent d-print-none">
          <div class="container-xl" style="width: 98%;">
            <div class="row text-center align-items-center flex-row-reverse">
              <div class="col-lg-auto ms-lg-auto">
                <ul class="list-inline list-inline-dots mb-0">
                  <li class="list-inline-item"><a href="https://github.com/tdragon6/Black/blob/main/LICENSE" target="_blank" class="link-secondary" style="text-decoration:none;">License</a></li>
                  <li class="list-inline-item"><a href="https://github.com/tdragon6/Black" target="_blank" class="link-secondary" style="text-decoration:none;">Github</a></li>
                  <li class="list-inline-item">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon text-pink icon-filled icon-inline" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M19.5 12.572l-7.5 7.428l-7.5 -7.428m0 0a5 5 0 1 1 7.5 -6.566a5 5 0 1 1 7.5 6.572" /></svg>
                      93dc2d41ae035a65
                  </li>
                </ul>
              </div>
              <div class="col-12 col-lg-auto mt-3 mt-lg-0">
                <ul class="list-inline list-inline-dots mb-0">
                  <li class="list-inline-item">
                    Copyright &copy; {{ year }} Black. All rights reserved.
                  </li>
                  <li class="list-inline-item">
                    <a>{{ black_version }}</a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </footer>
      </div>
     </div>
    <script src="/static/js/tabler.min.js"></script>
    <script src="/static/js/jquery.min.js"></script>
    <script src="/static/js/juqery.cookie.min.js"></script>
    <script src="/static/js/toastr.min.js"></script>
    <script src="/static/js/func/session.js"></script>
    <script src="/static/js/func/func.js"></script>
    <script src="/static/js/func/session_memfd.js"></script>
    <script>
        // 设置提示框属性
        toastr.options.positionClass = 'toast-top-center';
        toastr.options.timeOut = 3000;
        toastr.options.escapeHtml = true;
    </script>
    <script>
        // 获取sessid
        var sessid = '{{ sessid | safe }}';

        // 设置菜单栏指向的链接
        set_menu_link('{{ sessid | safe }}');

        // 原始常用文件数据分组步长
        var server_files_pages_size = 10;
    </script>
  </body>
</html>