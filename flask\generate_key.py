'''
    生成rssh连接的公私钥对
'''

from const import key_file, public_key_file, log_path
import paramiko
import logging
import os


logging.basicConfig(level=logging.INFO,
                    filename=log_path + '/flask.log',
                    filemode='a',
                    format='%(asctime)s - %(levelname)s - %(filename)s - %(funcName)s - %(lineno)s - %(message)s'
                    )


def generateSSHKey(private_filepath, public_filepath):
    '''
        生成rssh连接的公私钥对
    '''
    try:
        print(f"Starting SSH key generation...")
        print(f"Private key path: {private_filepath}")
        print(f"Public key path: {public_filepath}")

        # 确保目录存在
        private_dir = os.path.dirname(private_filepath)
        public_dir = os.path.dirname(public_filepath)

        print(f"Creating directories...")
        os.makedirs(private_dir, exist_ok=True)
        os.makedirs(public_dir, exist_ok=True)

        # 如果公钥路径是目录，删除它
        if os.path.isdir(public_filepath):
            import shutil
            shutil.rmtree(public_filepath)
            print(f'Removed directory at public key path: {public_filepath}')
            logging.info('Removed directory at public key path: ' + public_filepath)

        # 如果私钥路径是目录，删除它
        if os.path.isdir(private_filepath):
            import shutil
            shutil.rmtree(private_filepath)
            print(f'Removed directory at private key path: {private_filepath}')
            logging.info('Removed directory at private key path: ' + private_filepath)

        # 检查是否已存在有效的密钥对
        if os.path.isfile(private_filepath) and os.path.isfile(public_filepath):
            try:
                # 验证私钥是否有效
                test_key = paramiko.RSAKey.from_private_key_file(private_filepath)
                print('Valid SSH key pair already exists, skipping generation.')
                logging.info('SSH Key Existed and Valid, Skip Generate Key.')
                return
            except Exception as e:
                print(f'Existing keys are invalid ({e}), regenerating...')
                logging.warning(f'Existing keys are invalid: {e}')

        print("Generating new RSA key pair...")
        # 生成RSA密钥对
        key = paramiko.RSAKey.generate(2048)

        print("Writing private key file...")
        # 写入私钥文件
        key.write_private_key_file(private_filepath)

        print("Writing public key file...")
        # 写入公钥文件，使用标准SSH公钥格式
        with open(public_filepath, "w") as public:
            public.write("%s %s black@flask\n" % (key.get_name(), key.get_base64()))

        print("Setting file permissions...")
        # 设置正确的文件权限
        os.chmod(private_filepath, 0o600)
        os.chmod(public_filepath, 0o644)

        print("SSH key generation completed successfully!")
        logging.info('New SSH Key Generated Successfully.')

        # 验证生成的文件
        print(f"Private key size: {os.path.getsize(private_filepath)} bytes")
        print(f"Public key size: {os.path.getsize(public_filepath)} bytes")

    except Exception as e:
        print(f"Error generating SSH keys: {e}")
        logging.error(f'SSH Key Generation Failed: {e}')
        raise


if __name__ == "__main__":
    generateSSHKey(key_file, public_key_file)