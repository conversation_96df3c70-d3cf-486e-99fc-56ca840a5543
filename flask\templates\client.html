<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <title>Black - 客户端列表</title>
    <link rel="icon" href="/static/img/favicon.ico" type="image/x-icon">
    <link href="/static/css/tabler.min.css" rel="stylesheet"/>
    <link href="/static/css/tabler-flags.min.css" rel="stylesheet"/>
    <link href="/static/css/toastr.min.css" rel="stylesheet"/>
    <style>
      @import url('https://rsms.me/inter/inter.css');
      :root {
      	--tblr-font-sans-serif: 'Inter Var', -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;
      }
      body {
      	font-feature-settings: "cv03", "cv04", "cv11";
      }
    </style>
  </head>
  <body class="layout-fluid theme-light">
    <script src="/static/js/demo-theme.min.js"></script>
    <div class="modal modal-blur show" id="info" tabindex="-1" role="dialog" aria-modal="true" style="display: none;">
      <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">详细信息</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="mb-3">
              <label class="form-label">
                会话ID:&nbsp;&nbsp;
                <strong id="info-sessid" style="color: #206BC4;"></strong>
              </label>
            </div>
            <div class="row">
              <div class="col-lg-6">
                <div class="mb-3">
                  <label class="form-label">主机名:&nbsp;&nbsp;<strong id="info-hostname" style="color: #206BC4;"></strong></label>
                </div>
              </div>
              <div class="col-lg-6">
                <div class="mb-3">
                  <label class="form-label">地址:&nbsp;&nbsp;<strong id="info-address" style="color: #206BC4;"></strong></label>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-lg-6">
                <div class="mb-3">
                  <label class="form-label">用户名:&nbsp;&nbsp;<strong id="info-username" style="color: #206BC4;"></strong></label>
                </div>
              </div>
              <div class="col-lg-6">
                <div class="mb-3">
                  <label class="form-label">操作系统:&nbsp;&nbsp;<strong id="info-os" style="color: #206BC4;"></strong></label>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-lg-6">
                <div class="mb-3">
                  <label class="form-label">架构:&nbsp;&nbsp;<strong id="info-arch" style="color: #206BC4;"></strong></label>
                </div>
              </div>
              <div class="col-lg-6">
                <div class="mb-3">
                  <label class="form-label">归属地:&nbsp;&nbsp;<strong id="info-attribution" style="color: #206BC4;"></strong></label>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-lg-6">
                <div class="mb-3">
                  <label class="form-label">版本:&nbsp;&nbsp;<strong id="info-version" style="color: #206BC4;"></strong></label>
                </div>
              </div>
              <div class="col-lg-6">
                <div class="mb-3">
                  <label class="form-label">状态:&nbsp;&nbsp;<strong id="info-status" style="color: #206BC4;"></strong></label>
                </div>
              </div>
            </div>
            <div class="form-label">时间:&nbsp;&nbsp;<strong id="info-time" style="color: #206BC4;"></strong></div>
          </div>
          <div class="modal-body">
            <div class="row">
              <div class="col-lg-12">
                <div class="mb-3">
                  <label class="form-label">分组</label>
                  <input id="info-group" type="text" class="form-control">
                </div>
              </div>
              <div class="col-lg-12">
                <div>
                  <label class="form-label">备注</label>
                  <textarea id="info-mark" class="form-control" rows="3"></textarea>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <div id="mark-loader" class="spinner-border spinner-border-sm text-muted" role="status"></div>
            <a id="mark-btn" href="javascript:void(0);" onclick="save_client_info($('#info-group').val(), $('#info-mark').val(), $('#info-sessid').text());" class="btn btn-primary ms-auto">
              <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-check" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                <path d="M5 12l5 5l10 -10"></path>
              </svg>
              保存
            </a>
          </div>
        </div>
      </div>
    </div>
    <div class="modal modal-blur show" id="delete_client" tabindex="-1" aria-modal="true" role="dialog" style="display: none;">
      <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
        <div class="modal-content">
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          <div class="modal-status bg-danger"></div>
          <div class="modal-body text-center py-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="icon mb-2 text-danger icon-lg" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M12 9v2m0 4v.01"></path><path d="M5 19h14a2 2 0 0 0 1.84 -2.75l-7.1 -12.25a2 2 0 0 0 -3.5 0l-7.1 12.25a2 2 0 0 0 1.75 2.75"></path></svg>
            <h3 id="delete_client_title"></h3>
            <div id="delete_client_content" class="text-muted"></div>
          </div>
          <div class="modal-footer">
            <div id="deleteClient-loader" class="spinner-border spinner-border-sm text-muted" role="status"></div>
            <a id="deleteClient-btn" onclick="" href="javascript:void(0);" class="btn btn-danger w-100">确认</a>
          </div>
        </div>
      </div>
    </div>
    <div class="modal modal-blur show" id="calibration_clients" tabindex="-1" aria-modal="true" role="dialog" style="display: none;">
      <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
        <div class="modal-content">
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          <div class="modal-status bg-danger"></div>
          <div class="modal-body text-center py-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="icon mb-2 text-danger icon-lg" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M12 9v2m0 4v.01"></path><path d="M5 19h14a2 2 0 0 0 1.84 -2.75l-7.1 -12.25a2 2 0 0 0 -3.5 0l-7.1 12.25a2 2 0 0 0 1.75 2.75"></path></svg>
            <h3>确认要进行数据校准吗？</h3>
            <div class="text-muted">数据校准会获得当前最精确的在线客户端列表，但会丢失全部离线客户端和已设置的分组和备注</div>
          </div>
          <div class="modal-footer">
            <div id="calibrationClients-loader" class="spinner-border spinner-border-sm text-muted" role="status"></div>
            <a id="calibrationClients-btn" onclick="calibrationClients();" href="javascript:void(0);" class="btn btn-danger w-100">确认</a>
          </div>
        </div>
      </div>
    </div>
    <div class="modal modal-blur show" id="set_batch_group_mark" tabindex="-1" role="dialog" aria-modal="true" style="display: none;">
      <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">批量设置分组备注（已选择 <strong id="batch-mark-num"></strong> 个）</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="row">
              <div class="col-lg-12">
                <div class="mb-3">
                  <label class="form-label">分组</label>
                  <input id="batch-group" type="text" class="form-control">
                </div>
              </div>
              <div class="col-lg-12">
                <div>
                  <label class="form-label">备注</label>
                  <textarea id="batch-mark" class="form-control" rows="3"></textarea>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <div id="batch-mark-loader" class="spinner-border spinner-border-sm text-muted" role="status"></div>
            <a id="batch-mark-btn" href="javascript:void(0);" onclick="" class="btn btn-primary ms-auto">
              <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-check" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                <path d="M5 12l5 5l10 -10"></path>
              </svg>
              批量保存
            </a>
          </div>
        </div>
      </div>
    </div>
    <div class="page">
      <aside class="navbar navbar-vertical navbar-expand-lg navbar-dark">
        <div class="container-fluid">
          <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#sidebar-menu" aria-controls="sidebar-menu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
          </button>
          <h1 class="navbar-brand navbar-brand-autodark">
            <a href="/black/monitor">
              <img src="/static/img/logo-white.svg" width="110" height="32" alt="Black" class="navbar-brand-image">
            </a>
          </h1>
          <div class="collapse navbar-collapse" id="sidebar-menu">
            <ul class="navbar-nav pt-lg-3">
              <li class="nav-item">
                <a class="nav-link" href="/black/monitor">
                  <span class="nav-link-icon d-md-none d-lg-inline-block">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-home" width="32" height="32" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                      <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                      <path d="M5 12l-2 0l9 -9l9 9l-2 0"></path>
                      <path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7"></path>
                      <path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6"></path>
                   </svg>
                  </span>
                  <span class="nav-link-title" style="font-size: 15px">
                    监控台
                  </span>
                </a>
              </li>
              <li class="nav-item dropdown active">
                <a class="nav-link dropdown-toggle" href="javascript:void(0)" data-bs-toggle="dropdown" data-bs-auto-close="false" role="button" aria-expanded="false" >
                  <span class="nav-link-icon d-md-none d-lg-inline-block">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-device-desktop" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                      <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                      <path d="M3 4m0 1a1 1 0 0 1 1 -1h16a1 1 0 0 1 1 1v10a1 1 0 0 1 -1 1h-16a1 1 0 0 1 -1 -1z"></path>
                      <path d="M7 20l10 0"></path>
                      <path d="M9 16l0 4"></path>
                      <path d="M15 16l0 4"></path>
                   </svg>
                  </span>
                  <span class="nav-link-title" style="font-size: 15px">
                    客户端
                  </span>
                </a>
                <div class="dropdown-menu show">
                  <div class="dropdown-menu-columns">
                    <div class="dropdown-menu-column">
                      <a class="dropdown-item active" href="/black/client">
                        客户端列表
                      </a>
                      <a class="dropdown-item" href="/black/compile">
                        客户端生成
                      </a>
                    </div>
                  </div>
                </div>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="/black/server/files" >
                  <span class="nav-link-icon d-md-none d-lg-inline-block">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-files" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                       <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                       <path d="M15 3v4a1 1 0 0 0 1 1h4"></path>
                       <path d="M18 17h-7a2 2 0 0 1 -2 -2v-10a2 2 0 0 1 2 -2h4l5 5v7a2 2 0 0 1 -2 2z"></path>
                       <path d="M16 17v2a2 2 0 0 1 -2 2h-7a2 2 0 0 1 -2 -2v-10a2 2 0 0 1 2 -2h2"></path>
                    </svg>
                  </span>
                  <span class="nav-link-title" style="font-size: 15px">
                    常用文件
                  </span>
                </a>
              </li>
              <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="javascript:void(0)" data-bs-toggle="dropdown" data-bs-auto-close="false" role="button" aria-expanded="false" >
                  <span class="nav-link-icon d-md-none d-lg-inline-block">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-article" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                       <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                       <path d="M3 4m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z"></path>
                       <path d="M7 8h10"></path>
                       <path d="M7 12h10"></path>
                       <path d="M7 16h10"></path>
                    </svg>
                  </span>
                  <span class="nav-link-title" style="font-size: 15px">
                    日志监测
                  </span>
                </a>
                <div class="dropdown-menu show">
                  <div class="dropdown-menu-columns">
                    <div class="dropdown-menu-column">
                      <a class="dropdown-item" href="/black/log/flask">
                        业务日志
                      </a>
                      <a class="dropdown-item" href="/black/log/gunicorn">
                        Gunicorn日志
                      </a>
                    </div>
                  </div>
                </div>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="/black/notes" >
                  <span class="nav-link-icon d-md-none d-lg-inline-block">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-pencil" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                      <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                      <path d="M4 20h4l10.5 -10.5a1.5 1.5 0 0 0 -4 -4l-10.5 10.5v4"></path>
                      <path d="M13.5 6.5l4 4"></path>
                    </svg>
                  </span>
                  <span class="nav-link-title" style="font-size: 15px">
                    备忘录
                  </span>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="/black/setting" >
                  <span class="nav-link-icon d-md-none d-lg-inline-block">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-settings" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                      <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                      <path d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z"></path>
                      <path d="M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"></path>
                    </svg>
                  </span>
                  <span class="nav-link-title" style="font-size: 15px">
                    设置
                  </span>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </aside>
      <header class="navbar navbar-expand-md navbar-light d-none d-lg-flex d-print-none">
        <div class="container-xl">
          <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu" aria-controls="navbar-menu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
          </button>
          <div class="navbar-nav flex-row order-md-last">
            <div class="d-none d-md-flex">
              <a href="?theme=dark" class="nav-link px-0 hide-theme-dark" title="暗黑模式" data-bs-toggle="tooltip"
		   data-bs-placement="bottom">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" /></svg>
              </a>
              <a href="?theme=light" class="nav-link px-0 hide-theme-light" title="明亮模式" data-bs-toggle="tooltip"
		   data-bs-placement="bottom">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="12" cy="12" r="4" /><path d="M3 12h1m8 -9v1m8 8h1m-9 8v1m-6.4 -15.4l.7 .7m12.1 -.7l-.7 .7m0 11.4l.7 .7m-12.1 -.7l-.7 .7" /></svg>
              </a>
            </div>
          </div>
          <div class="collapse navbar-collapse" id="navbar-menu">
            <div class="nav-item dropdown">
              <a href="javascript:void(0)" class="nav-link d-flex lh-1 text-reset p-0" data-bs-toggle="dropdown">
                <span class="avatar avatar-sm" style="background-image: url(/static/img/avatar.jpg)"></span>
                <div class="d-none d-xl-block ps-2">
                  <div>{{ username }}</div>
                  <div class="mt-1 small text-muted">Hacker</div>
                </div>
              </a>
              <div class="dropdown-menu dropdown-menu-arrow">
                <a href="/black/setting" class="dropdown-item">
                  <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-settings" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z"></path>
                    <path d="M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"></path>
                  </svg>&nbsp;
                  设置
                </a>
                <a href="https://github.com/tdragon6/Supershell" target="_blank" class="dropdown-item">
                  <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-brand-github" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M9 19c-4.3 1.4 -4.3 -2.5 -6 -3m12 5v-3.5c0 -1 .1 -1.4 -.5 -2c2.8 -.3 5.5 -1.4 5.5 -6a4.6 4.6 0 0 0 -1.3 -3.2a4.2 4.2 0 0 0 -.1 -3.2s-1.1 -.3 -3.5 1.3a12.3 12.3 0 0 0 -6.2 0c-2.4 -1.6 -3.5 -1.3 -3.5 -1.3a4.2 4.2 0 0 0 -.1 3.2a4.6 4.6 0 0 0 -1.3 3.2c0 4.6 2.7 5.7 5.5 6c-.6 .6 -.6 1.2 -.5 2v3.5"></path>
                  </svg>&nbsp;
                  Github
                </a>
                <div class="dropdown-divider"></div>
                <a href="javascript:void(0);" onclick="$.removeCookie('token',{ path: '/'});location.reload();" class="dropdown-item">
                  <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-logout" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M14 8v-2a2 2 0 0 0 -2 -2h-7a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2 -2v-2"></path>
                    <path d="M7 12h14l-3 -3m0 6l3 -3"></path>
                  </svg>&nbsp;
                  注销
                </a>
              </div>
            </div>
          </div>
        </div>
      </header>
      <div class="page-wrapper">
        <div class="page-body">
          <div class="container-xl">
            <div class="row row-deck row-cards">
              <div class="col-12">
                <div class="card">
                  <div class="card-header">
                    <span id="loader"></span>
                    &nbsp;&nbsp;
                    <h3 class="card-title">客户端列表</h3>
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <a class="nav-link" href="javascript:void(0)" onclick="update_client_memory(search_text, filter_dict, filter_choose)" title="刷新" data-bs-toggle="tooltip" data-bs-placement="top">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-refresh" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"></path>
                        <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"></path>
                      </svg>
                    </a>
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <a class="nav-link" href="javascript:void(0)" onclick="show_calibrationClients();" title="数据校准" data-bs-toggle="tooltip" data-bs-placement="top">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-tool" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                         <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                         <path d="M7 10h3v-3l-3.5 -3.5a6 6 0 0 1 8 8l6 6a2 2 0 0 1 -3 3l-6 -6a6 6 0 0 1 -8 -8l3.5 3.5"></path>
                      </svg>
                    </a>
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <a class="nav-link" href="javascript:void(0)" onclick="show_deleteClient('disconnectAll', '')" title="断开全部连接" data-bs-toggle="tooltip" data-bs-placement="top">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-x" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                          <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                          <path d="M18 6l-12 12"></path>
                          <path d="M6 6l12 12"></path>
                      </svg>
                    </a>
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <a class="nav-link" href="javascript:void(0)" onclick="show_deleteClient('deleteAllOff', '')" title="删除全部离线记录" data-bs-toggle="tooltip" data-bs-placement="top">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-trash" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                         <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                         <path d="M4 7l16 0"></path>
                         <path d="M10 11l0 6"></path>
                         <path d="M14 11l0 6"></path>
                         <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12"></path>
                         <path d="M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3"></path>
                      </svg>
                    </a>
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <a class="nav-link" href="javascript:void(0)" data-bs-toggle="dropdown">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-list" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M9 6l11 0"></path>
                        <path d="M9 12l11 0"></path>
                        <path d="M9 18l11 0"></path>
                        <path d="M5 6l0 .01"></path>
                        <path d="M5 12l0 .01"></path>
                        <path d="M5 18l0 .01"></path>
                      </svg>
                    </a>
                    <div class="dropdown-menu">
                      <a class="dropdown-item" href="javascript:void(0)" onclick="show_deleteBatchClients('disconnectBatch');">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-x" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                          <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                          <path d="M18 6l-12 12"></path>
                          <path d="M6 6l12 12"></path>
                        </svg>
                        &nbsp;&nbsp;批量断开在线连接
                      </a>
                      <a class="dropdown-item" href="javascript:void(0)" onclick="show_deleteBatchClients('deleteBatch');">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-trash" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                          <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                          <path d="M4 7l16 0"></path>
                          <path d="M10 11l0 6"></path>
                          <path d="M14 11l0 6"></path>
                          <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12"></path>
                          <path d="M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3"></path>
                        </svg>
                        &nbsp;&nbsp;批量删除离线记录
                      </a>
                      <a class="dropdown-item" href="javascript:void(0)" onclick="show_setBatchGroupMark();">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-pencil" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                          <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                          <path d="M4 20h4l10.5 -10.5a2.828 2.828 0 1 0 -4 -4l-10.5 10.5v4"></path>
                          <path d="M13.5 6.5l4 4"></path>
                        </svg>
                        &nbsp;&nbsp;批量设置分组备注
                      </a>
                    </div>
                    <div class="ms-auto text-muted input-icon">
                      <input id="search" type="text" class="form-control form-control-sm" placeholder="搜索">
                      <label class="input-icon-addon">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="20" height="20" viewBox="0 0 28 28" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path><path d="M21 21l-6 -6"></path></svg>
                      </label>
                    </div>
                  </div>
                  <div class="table-responsive" style="overflow: visible;">
                    <table class="table table-hover card-table table-vcenter">
                      <thead>
                        <tr>
                          <th><input name="all_select" class="form-check-input" type="checkbox" onclick="set_all_checkbox();"></th>
                          <th style="font-size: 14px;">#</th>
                          <th style="font-size: 14px;">地址</th>
                          <th style="font-size: 14px;">备注</th>
                          <th style="font-size: 14px;">用户名</th>
                          <th style="font-size: 14px;">
                            <button class="table-sort" data-bs-toggle="dropdown">归属地</button>
                            <div id="filter-attribution" class="dropdown-menu dropdown-menu-arrow" style="font-size: 10px;">
                            </div>
                          </th>
                          <th style="font-size: 14px;">
                            <button class="table-sort" data-bs-toggle="dropdown">系统</button>
                            <div id="filter-os" class="dropdown-menu dropdown-menu-arrow" style="font-size: 10px;">
                            </div>
                          </th>
                          <th style="font-size: 14px;">
                            <button class="table-sort" data-bs-toggle="dropdown">架构</button>
                            <div id="filter-arch" class="dropdown-menu dropdown-menu-arrow" style="font-size: 10px;">
                            </div>
                          </th>
                          <th style="font-size: 14px;">时间</th>
                          <th style="font-size: 14px;">
                            <button class="table-sort" data-bs-toggle="dropdown">版本</button>
                            <div id="filter-version" class="dropdown-menu dropdown-menu-arrow" style="font-size: 10px;">
                            </div>
                          </th>
                          <th style="font-size: 14px;">
                            <button class="table-sort" data-bs-toggle="dropdown">分组</button>
                            <div id="filter-group" class="dropdown-menu dropdown-menu-arrow" style="font-size: 10px;">
                            </div>
                          </th>
                          <th style="font-size: 14px;">
                            <button class="table-sort" data-bs-toggle="dropdown">状态</button>
                            <div id="filter-status" class="dropdown-menu dropdown-menu-arrow" style="font-size: 10px;">
                            </div>
                          </th>
                          <th style="font-size: 14px;">操作</th>
                        </tr>
                      </thead>
                      <tbody id="client_table">
                      </tbody>
                    </table>
                  </div>
                  <div class="card-footer d-flex align-items-center">
                    <div class="text-secondary">
                      设置
                      <div class="mx-2 d-inline-block">
                        <input id="page-change-num" type="text" class="form-control form-control-sm" size="3">
                      </div>
                      条/页
                    </div>
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <p id="pages-info" class="m-0 text-muted col-3">无数据</p>
                    <ul id="pages-button" class="pagination m-0 ms-auto" style="overflow: overlay;"></ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <footer class="footer footer-transparent d-print-none">
          <div class="container-xl" style="width: 98%;">
            <div class="row text-center align-items-center flex-row-reverse">
              <div class="col-lg-auto ms-lg-auto">
                <ul class="list-inline list-inline-dots mb-0">
                  <li class="list-inline-item"><a href="https://github.com/tdragon6/Supershell/blob/main/LICENSE" target="_blank" class="link-secondary" style="text-decoration:none;">License</a></li>
                  <li class="list-inline-item"><a href="https://github.com/tdragon6/Supershell" target="_blank" class="link-secondary" style="text-decoration:none;">Github</a></li>
                  <li class="list-inline-item">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon text-pink icon-filled icon-inline" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M19.5 12.572l-7.5 7.428l-7.5 -7.428m0 0a5 5 0 1 1 7.5 -6.566a5 5 0 1 1 7.5 6.572" /></svg>
                      93dc2d41ae035a65
                  </li>
                </ul>
              </div>
              <div class="col-12 col-lg-auto mt-3 mt-lg-0">
                <ul class="list-inline list-inline-dots mb-0">
                  <li class="list-inline-item">
                    Copyright &copy; {{ year }} Black. All rights reserved.
                  </li>
                  <li class="list-inline-item">
                    <a>{{ black_version }}</a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
    <script src="/static/js/tabler.min.js"></script>
    <script src="/static/js/jquery.min.js"></script>
    <script src="/static/js/juqery.cookie.min.js"></script>
    <script src="/static/js/clipboard.js"></script>
    <script src="/static/js/toastr.min.js"></script>
    <script src="/static/js/purify.min.js"></script>
    <script src="/static/js/func/client.js"></script>
    <script src="/static/js/func/func.js"></script>
    <script>
      // 设置提示框属性
      toastr.options.positionClass = 'toast-top-center';
      toastr.options.timeOut = 3000;
      toastr.options.escapeHtml = true;
    </script>
    <script>
      var search_text = ''; // 搜索文本
      
      // 原始客户端数据分组步长
      var pages_size = 10;
      if ($.cookie('client_page_num') !== undefined){
        if (check_positive_integer($.cookie('client_page_num'))){
          pages_size = Number($.cookie('client_page_num'));
        }
      }
      $('#page-change-num').val(pages_size.toString());

      var clients_dict = {}; // 原始客户端字典数据
      var clients_list = []; // 原始列表类型客户端数据

      // 全部的筛选项
      var filter_dict = {};

      // 选择的筛选项
      var filter_choose = {};

      // 更新获取客户端列表（从redis内存中读取）
      update_client_memory(search_text, filter_dict, filter_choose);

      //回车提交每页条数配置
      page_enter_listen();

      // 回车提交搜索
      search_enter_listen();
    </script>
  </body>
</html>