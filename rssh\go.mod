module github.com/NHAS/reverse_ssh

go 1.23.1

require (
	github.com/ActiveState/termtest/conpty v0.5.0
	github.com/bodgit/ntlmssp v0.0.0-20240506230425-31973bb52d9b
	github.com/creack/pty v1.1.24
	github.com/fatih/color v1.18.0
	github.com/glebarez/sqlite v1.11.0
	github.com/go-ping/ping v1.2.0
	github.com/inetaf/tcpproxy v0.0.0-20250222171855-c4b9df066048
	github.com/pkg/sftp v1.13.9
	github.com/valyala/fasthttp v1.62.0
	golang.org/x/crypto v0.38.0
	golang.org/x/net v0.40.0
	golang.org/x/sys v0.33.0
	gorm.io/gorm v1.25.12
	gvisor.dev/gvisor v0.0.0-20250403230555-2b1f43f26fbb
)

require (
	github.com/Azure/go-ansiterm v0.0.0-20250102033503-faa5f7b0171c // indirect
	github.com/andybalholm/brotli v1.1.1 // indirect
	github.com/bodgit/windows v1.0.1 // indirect
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/glebarez/go-sqlite v1.22.0 // indirect
	github.com/google/btree v1.1.3 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/klauspost/compress v1.18.0 // indirect
	github.com/kr/fs v0.1.0 // indirect
	github.com/mattn/go-colorable v0.1.14 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/ncruces/go-strftime v0.1.9 // indirect
	github.com/remyoudompheng/bigfft v0.0.0-20230129092748-24d4a6f8daec // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	golang.org/x/exp v0.0.0-20250305212735-054e65f0b394 // indirect
	golang.org/x/sync v0.14.0 // indirect
	golang.org/x/text v0.25.0 // indirect
	golang.org/x/time v0.11.0 // indirect
	modernc.org/libc v1.62.1 // indirect
	modernc.org/mathutil v1.7.1 // indirect
	modernc.org/memory v1.9.1 // indirect
	modernc.org/sqlite v1.37.0 // indirect
)
