<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <title>Supershell -文件管理</title>
    <link rel="icon" href="/static/img/favicon.ico" type="image/x-icon">
    <link href="/static/css/tabler.min.css" rel="stylesheet"/>
    <link href="/static/css/zTreeStyle/zTreeStyle.css" rel="stylesheet"/>
    <link href="/static/css/toastr.min.css" rel="stylesheet"/>
    <style>
      @import url('https://rsms.me/inter/inter.css');
      :root {
      	--tblr-font-sans-serif: 'Inter Var', -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;
      }
      body {
      	font-feature-settings: "cv03", "cv04", "cv11";
      }
    </style>
  </head>
  <body class="layout-fluid">
    <script src="/static/js/demo-theme.min.js"></script>
    <input id="local-file-upload" type="file" style="display:none;">
    <div class="modal modal-blur show" id="upload_file" tabindex="-1" role="dialog" aria-modal="true" style="display: none;">
      <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">上传文件&nbsp;&nbsp;<div id="upload-loader" class="ms-auto spinner-border spinner-border-sm text-muted" role="status" style="display: none;"></div></h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body" style="margin-bottom: -22px; margin-top: -15px;">
            <label class="form-label">
              当前路径:&nbsp;&nbsp;
              <strong id="upload_file_path" style="color: #206BC4;"></strong>
            </label>
          </div>
          <div class="card">
            <div class="card-header">
              <span id="serverFiles-loader"></span>
              &nbsp;&nbsp;
              <h3 class="card-title">常用文件列表</h3>
              &nbsp;&nbsp;&nbsp;&nbsp;
              <a class="nav-link" href="javascript:void(0)" onclick="get_server_files_list();" title="刷新" data-bs-toggle="tooltip" data-bs-placement="top">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-refresh" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                  <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                  <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"></path>
                  <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"></path>
                </svg>
              </a>
              &nbsp;&nbsp;&nbsp;&nbsp;
              <a class="nav-link" href="/black/server/files" target="_blank" title="进入常用文件管理" data-bs-toggle="tooltip" data-bs-placement="top">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-files" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                   <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                   <path d="M15 3v4a1 1 0 0 0 1 1h4"></path>
                   <path d="M18 17h-7a2 2 0 0 1 -2 -2v-10a2 2 0 0 1 2 -2h4l5 5v7a2 2 0 0 1 -2 2z"></path>
                   <path d="M16 17v2a2 2 0 0 1 -2 2h-7a2 2 0 0 1 -2 -2v-10a2 2 0 0 1 2 -2h2"></path>
                </svg>
              </a>
              <div class="ms-auto">
                <a href="javascript:void(0);" onclick="$('#local-file-upload').click();" class="link-secondary ms-2" data-bs-toggle="tooltip" data-bs-original-title="上传本地文件" data-bs-placement="top">
                  <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-upload" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2"></path>
                    <path d="M7 9l5 -5l5 5"></path>
                    <path d="M12 4l0 12"></path>
                  </svg>
                </a>
              </div>
            </div>
            <div class="table-responsive" style="overflow: visible;">
              <table class="table table-hover card-table table-vcenter">
              <thead>
                <tr>
                  <th style="font-size: 14px;">#</th>
                  <th style="font-size: 14px;">
                    <button id="field_server_file_name" class="table-sort" onclick="sort_by_field_server_files($(this).attr('id'), true)">文件名</button>
                  </th>
                  <th style="font-size: 14px;">
                    <button id="field_server_file_size" class="table-sort" onclick="sort_by_field_server_files($(this).attr('id'), true)">文件大小</button>
                  </th>
                  <th style="font-size: 14px;">
                    <button id="field_server_file_time" class="table-sort" onclick="sort_by_field_server_files($(this).attr('id'), true)">修改时间</button>
                  </th>
                  <th style="font-size: 14px;">操作</th>
                </tr>
              </thead>
              <tbody id="server_files_table">
              </tbody>
            </table>
            </div>
            <div class="card-footer d-flex align-items-center">
              <div class="text-secondary">
                设置
                <div class="mx-2 d-inline-block">
                  <input id="page-change-num-upload" type="text" class="form-control form-control-sm" size="3">
                </div>
                条/页
              </div>
              &nbsp;&nbsp;&nbsp;&nbsp;
              <p id="serverFilesPages-info" class="m-0 text-muted col-4">无数据</p>
              <ul id="serverFilesPages-button" class="pagination m-0 ms-auto" style="overflow: overlay;"></ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="modal modal-blur show" id="make_dir" tabindex="-1" role="dialog" aria-modal="true" style="display: none;">
      <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">新建目录</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="mb-3">
              <label class="form-label">
                新建目录:&nbsp;&nbsp;
                <strong id="make_dir_path" style="color: #206BC4;"></strong>
              </label>
            </div>
            <div class="mb-3">
              <label class="form-label">输入目录名:</label>
              <input id="make_dir_name" oninput="" type="text" class="form-control">
            </div>
          </div>
          <div class="modal-footer">
            <div id="mkdir-loader" class="spinner-border spinner-border-sm text-muted" role="status"></div>
            <a id="mkdir-btn" href="javascript:void(0);" onclick="mkdir($('#make_dir_path').text(), sessid);" class="btn btn-primary ms-auto">
              <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-check" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                <path d="M5 12l5 5l10 -10"></path>
              </svg>
              确定
            </a>
          </div>
        </div>
      </div>
    </div>
    <div class="modal modal-blur show" id="rename_path" tabindex="-1" role="dialog" aria-modal="true" style="display: none;">
      <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">重命名</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="mb-3">
              <label class="form-label">
                原始路径:&nbsp;&nbsp;
                <strong id="ori_rename_path" style="color: #206BC4;"></strong>
              </label>
            </div>
            <div class="mb-3">
              <label class="form-label">
                重命名为:&nbsp;&nbsp;
                <strong id="new_rename_path" style="color: #206BC4;"></strong>
              </label>
            </div>
            <div class="mb-3">
              <label class="form-label">输入修改名:</label>
              <input id="rename_name" oninput="" type="text" class="form-control">
            </div>
          </div>
          <div class="modal-footer">
            <div id="renamePath-loader" class="spinner-border spinner-border-sm text-muted" role="status"></div>
            <a id="renamePath-btn" href="javascript:void(0);" onclick="renamePath($('#ori_rename_path').text(), $('#new_rename_path').text(), sessid);" class="btn btn-primary ms-auto">
              <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-check" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                <path d="M5 12l5 5l10 -10"></path>
              </svg>
              确定
            </a>
          </div>
        </div>
      </div>
    </div>
    <div class="modal modal-blur show" id="delete_path" tabindex="-1" aria-modal="true" role="dialog" style="display: none;">
      <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
        <div class="modal-content">
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          <div class="modal-status bg-danger"></div>
          <div class="modal-body text-center py-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="icon mb-2 text-danger icon-lg" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M12 9v2m0 4v.01"></path><path d="M5 19h14a2 2 0 0 0 1.84 -2.75l-7.1 -12.25a2 2 0 0 0 -3.5 0l-7.1 12.25a2 2 0 0 0 1.75 2.75"></path></svg>
            <h3 id="delete_path_title"></h3>
            <div id="delete_path_content" class="text-muted"></div>
          </div>
          <div class="modal-footer">
            <div id="deletePath-loader" class="spinner-border spinner-border-sm text-muted" role="status"></div>
            <a id="deletePath-btn" href="javascript:void(0);" onclick="" class="btn btn-danger w-100 ms-auto">确认</a>
          </div>
        </div>
      </div>
    </div>
    <div class="modal modal-blur show" id="edit_file" tabindex="-1" role="dialog" aria-modal="true" style="display: none;">
      <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">编辑文件</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="mb-3">
              <label class="form-label">
                文件路径:&nbsp;&nbsp;
                <strong id="edit_file_path" style="color: #206BC4;"></strong>
              </label>
            </div>
            <div class="mb-3">
              <label class="form-label">
                  修改内容:
              </label>
              <textarea id="edit_file_content" class="form-control" rows="20"></textarea>
            </div>
          </div>
          <div class="modal-footer">
            <div id="editFile-loader" class="spinner-border spinner-border-sm text-muted" role="status"></div>
            <a id="editFile-btn" href="javascript:void(0);" onclick="editFile($('#edit_file_path').text(), $('#edit_file_content').val(), sessid);" class="btn btn-primary ms-auto">
              <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-check" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                <path d="M5 12l5 5l10 -10"></path>
              </svg>
              保存
            </a>
          </div>
        </div>
      </div>
    </div>
    <div class="modal modal-blur show" id="new_file" tabindex="-1" role="dialog" aria-modal="true" style="display: none;">
      <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">新建文件</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="mb-3">
              <label class="form-label">
                文件路径:&nbsp;&nbsp;
                <strong id="new_file_path" style="color: #206BC4;"></strong>
              </label>
            </div>
            <div class="mb-3">
              <label class="form-label">输入文件名:</label>
              <input id="new_file_name" oninput="" type="text" class="form-control">
            </div>
            <div class="mb-3">
              <label class="form-label">
                  文件内容:
              </label>
              <textarea id="new_file_content" class="form-control" rows="20"></textarea>
            </div>
          </div>
          <div class="modal-footer">
            <div id="newFile-loader" class="spinner-border spinner-border-sm text-muted" role="status"></div>
            <a id="newFile-btn" href="javascript:void(0);" onclick="newFile($('#new_file_path').text(), $('#new_file_content').val(), sessid);" class="btn btn-primary ms-auto">
              <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-check" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                <path d="M5 12l5 5l10 -10"></path>
              </svg>
              确定
            </a>
          </div>
        </div>
      </div>
    </div>
    <div class="page">
      <header class="navbar navbar-expand-md navbar-light d-print-none">
        <div class="container-xl">
          <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu" aria-controls="navbar-menu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
          </button>
          <h1 class="navbar-brand navbar-brand-autodark d-none-navbar-horizontal pe-0 pe-md-3">
            <a href="/black/monitor">
              <img src="/static/img/logo.svg" width="110" height="32" alt="Black" class="navbar-brand-image">
            </a>
          </h1>
          <div class="navbar-nav flex-row order-md-last">
            <div class="d-none d-md-flex me-3">
              <a href="?arg={{ sessid | safe }}&theme=dark" class="nav-link px-0 hide-theme-dark" title="暗黑模式" data-bs-toggle="tooltip"
		   data-bs-placement="bottom">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" /></svg>
              </a>
              <a href="?arg={{ sessid | safe }}&theme=light" class="nav-link px-0 hide-theme-light" title="明亮模式" data-bs-toggle="tooltip"
		   data-bs-placement="bottom">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="12" cy="12" r="4" /><path d="M3 12h1m8 -9v1m8 8h1m-9 8v1m-6.4 -15.4l.7 .7m12.1 -.7l-.7 .7m0 11.4l.7 .7m-12.1 -.7l-.7 .7" /></svg>
              </a>
            </div>
            <div class="nav-item dropdown">
              <a href="javascript:void(0)" class="nav-link d-flex lh-1 text-reset p-0" data-bs-toggle="dropdown">
                <span class="avatar avatar-sm" style="background-image: url(/static/img/avatar.jpg)"></span>
                <div class="d-none d-xl-block ps-2">
                  <div>{{ username }}</div>
                  <div class="mt-1 small text-muted">Hacker</div>
                </div>
              </a>
              <div class="dropdown-menu dropdown-menu-arrow">
                <a href="/black/setting" target="_blank" class="dropdown-item">
                  <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-settings" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z"></path>
                    <path d="M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"></path>
                  </svg>&nbsp;
                  设置
                </a>
                <a href="https://github.com/tdragon6/Supershell" target="_blank" class="dropdown-item">
                  <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-brand-github" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M9 19c-4.3 1.4 -4.3 -2.5 -6 -3m12 5v-3.5c0 -1 .1 -1.4 -.5 -2c2.8 -.3 5.5 -1.4 5.5 -6a4.6 4.6 0 0 0 -1.3 -3.2a4.2 4.2 0 0 0 -.1 -3.2s-1.1 -.3 -3.5 1.3a12.3 12.3 0 0 0 -6.2 0c-2.4 -1.6 -3.5 -1.3 -3.5 -1.3a4.2 4.2 0 0 0 -.1 3.2a4.6 4.6 0 0 0 -1.3 3.2c0 4.6 2.7 5.7 5.5 6c-.6 .6 -.6 1.2 -.5 2v3.5"></path>
                  </svg>&nbsp;
                  Github
                </a>
                <div class="dropdown-divider"></div>
                <a href="javascript:void(0);" onclick="$.removeCookie('token',{ path: '/'});location.reload();" class="dropdown-item">
                  <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-logout" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M14 8v-2a2 2 0 0 0 -2 -2h-7a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2 -2v-2"></path>
                    <path d="M7 12h14l-3 -3m0 6l3 -3"></path>
                  </svg>&nbsp;
                  注销
                </a>
              </div>
            </div>
          </div>
        </div>
      </header>
      <header class="navbar-expand-md">
        <div class="collapse navbar-collapse" id="navbar-menu">
          <div class="navbar navbar-light">
            <div class="container-xl">
              <ul class="navbar-nav">
                <li class="nav-item">
                  <a class="nav-link" href="" id="session-info">
                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-info-square-rounded" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M12 8h.01"></path>
                        <path d="M11 12h1v4h1"></path>
                        <path d="M12 3c7.2 0 9 1.8 9 9s-1.8 9 -9 9s-9 -1.8 -9 -9s1.8 -9 9 -9z"></path>
                      </svg>
                    </span>
                    <span class="nav-link-title">
                      会话信息
                    </span>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" href="" id="session-shell">
                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-brand-tabler" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                         <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                         <path d="M8 9l3 3l-3 3"></path>
                         <path d="M13 15l3 0"></path>
                         <path d="M4 4m0 4a4 4 0 0 1 4 -4h8a4 4 0 0 1 4 4v8a4 4 0 0 1 -4 4h-8a4 4 0 0 1 -4 -4z"></path>
                      </svg>
                    </span>
                    <span class="nav-link-title">
                      交互终端
                    </span>
                  </a>
                </li>
                <li class="nav-item active">
                  <a class="nav-link" href="" id="session-files">
                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-file-description" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M14 3v4a1 1 0 0 0 1 1h4"></path>
                        <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"></path>
                        <path d="M9 17h6"></path>
                        <path d="M9 13h6"></path>
                      </svg>
                    </span>
                    <span class="nav-link-title">
                      文件管理
                    </span>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" href="" id="session-memfd">
                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-file-x" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                         <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                         <path d="M14 3v4a1 1 0 0 0 1 1h4"></path>
                         <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"></path>
                       <path d="M10 12l4 4m0 -4l-4 4"></path>
                      </svg>
                    </span>
                    <span class="nav-link-title">
                      内存执行
                    </span>
                  </a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" href="" id="session-advanced">
                    <span class="nav-link-icon d-md-none d-lg-inline-block">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-swords" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                         <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                         <path d="M21 3v5l-11 9l-4 4l-3 -3l4 -4l9 -11z"></path>
                         <path d="M5 13l6 6"></path>
                         <path d="M14.32 17.32l3.68 3.68l3 -3l-3.365 -3.365"></path>
                         <path d="M10 5.5l-2 -2.5h-5v5l3 2.5"></path>
                      </svg>
                    </span>
                    <span class="nav-link-title">
                      进阶功能
                    </span>
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </header>
      <div class="page-wrapper">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">文件管理</h3>
          </div>
          <div class="card">
            <div class="row g-0">
              <div class="col-3 d-none d-md-block border-end">
                <div class="card-body">
                    <h4 class="text-muted">
                        目录树&nbsp;&nbsp;
                        <span id="tree-loader"></span>
                    </h4>
                  <div id="tree" class="ztree" style="overflow: scroll; height: 70vh;"></div>
                </div>
              </div>
              <div class="col d-flex flex-column">
                  <div class="card-body border-bottom">
                    <ol id="bread_path" class="breadcrumb mb-3" aria-label="breadcrumbs">
                    </ol>
                    <div class="row mb-2">
                      <div class="input-group">
                        <span id="loader" class="input-group-text"></span>
                        <input id="file_path" type="text" class="form-control form-control">
                        <span class="input-group-text">
                          <a href="javascript:void(0);" onclick="get_files_list($('#file_path').val(), sessid);" class="link-secondary" data-bs-toggle="tooltip" data-bs-original-title="进入目录" data-bs-placement="top">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-corner-down-left" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                              <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                              <path d="M18 6v6a3 3 0 0 1 -3 3h-10l4 -4m0 8l-4 -4"></path>
                            </svg>
                          </a>
                          <a href="javascript:void(0);" onclick="copyText($('#file_path').val());" class="link-secondary ms-2" data-bs-toggle="tooltip" data-bs-original-title="复制路径" data-bs-placement="top">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-copy" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                              <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                              <path d="M8 8m0 2a2 2 0 0 1 2 -2h8a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-8a2 2 0 0 1 -2 -2z"></path>
                              <path d="M16 8v-2a2 2 0 0 0 -2 -2h-8a2 2 0 0 0 -2 2v8a2 2 0 0 0 2 2h2"></path>
                            </svg>
                          </a>
                          <a href="javascript:void(0);" onclick="show_mkdir();" class="link-secondary ms-2" data-bs-toggle="tooltip" data-bs-original-title="新建目录" data-bs-placement="top">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-folder-plus" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                              <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                              <path d="M5 4h4l3 3h7a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-11a2 2 0 0 1 2 -2"></path>
                              <path d="M12 10l0 6"></path>
                              <path d="M9 13l6 0"></path>
                            </svg>
                          </a>
                          <a href="javascript:void(0);" onclick="show_newFile();" class="link-secondary ms-2" data-bs-toggle="tooltip" data-bs-original-title="新建文件" data-bs-placement="top">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-file-plus" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                              <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                              <path d="M14 3v4a1 1 0 0 0 1 1h4"></path>
                              <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"></path>
                              <path d="M12 11l0 6"></path>
                              <path d="M9 14l6 0"></path>
                            </svg>
                          </a>
                          <a href="javascript:void(0);" onclick="show_uploadFile();" class="link-secondary ms-2" data-bs-toggle="tooltip" data-bs-original-title="上传文件" data-bs-placement="top">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-upload" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                              <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                              <path d="M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2"></path>
                              <path d="M7 9l5 -5l5 5"></path>
                              <path d="M12 4l0 12"></path>
                            </svg>
                          </a>
                          <div class="ms-2">
                            <a id="bell-info" class="nav-link" href="javascript:void(0)" onclick="" data-bs-toggle="dropdown">
                              <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-bell" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                 <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                 <path d="M10 5a2 2 0 0 1 4 0a7 7 0 0 1 4 6v3a4 4 0 0 0 2 3h-16a4 4 0 0 0 2 -3v-3a7 7 0 0 1 4 -6"></path>
                                 <path d="M9 17v1a3 3 0 0 0 6 0v-1"></path>
                              </svg>
                            </a>
                            <div class="dropdown-menu dropdown-menu-arrow dropdown-menu-end dropdown-menu-card">
                              <div class="card">
                                <div class="card-header">
                                  <h3 class="card-title">上传列表</h3>
                                </div>
                                <div id="upload-progress" class="list-group list-group-flush list-group-hoverable">
                                </div>
                              </div>
                            </div>
                          </div>
                        </span>
                      </div>
                    </div>
                    <div class="card">
                        <div class="table-responsive" style="overflow: visible;">
                          <table class="table table-hover card-table table-vcenter">
                            <thead>
                              <tr>
                                <th style="font-size: 14px;">
                                  <button id="field_file_type" class="table-sort" onclick="sort_by_field($(this).attr('id'), true)">类型</button>
                                </th>
                                <th style="font-size: 14px;">
                                  <button id="field_file_name" class="table-sort" onclick="sort_by_field($(this).attr('id'), true)">文件名</button>
                                </th>
                                <th style="font-size: 14px;">
                                  <button id="field_file_size" class="table-sort" onclick="sort_by_field($(this).attr('id'), true)">大小</button>
                                </th>
                                <th style="font-size: 14px;">
                                  <button id="field_file_mode" class="table-sort" onclick="sort_by_field($(this).attr('id'), true)">权限</button>
                                </th>
                                <th style="font-size: 14px;">
                                  <button id="field_file_time" class="table-sort" onclick="sort_by_field($(this).attr('id'), true)">时间</button>
                                </th>
                                <th style="font-size: 14px;">操作</th>
                              </tr>
                            </thead>
                            <tbody id="files_table">
                            </tbody>
                          </table>
                        </div>
                        <div class="card-footer d-flex align-items-center">
                          <div class="text-secondary">
                            设置
                            <div class="mx-2 d-inline-block">
                              <input id="page-change-num" type="text" class="form-control form-control-sm" size="3">
                            </div>
                            条/页
                          </div>
                          &nbsp;&nbsp;&nbsp;&nbsp;
                          <p id="pages-info" class="m-0 text-muted col-3">无数据</p>
                          <ul id="pages-button" class="pagination m-0 ms-auto" style="overflow: overlay;"></ul>
                        </div>
                    </div>
                  </div>
              </div>
            </div>
          </div>
        </div>
        <footer class="footer footer-transparent d-print-none">
          <div class="container-xl" style="width: 98%;">
            <div class="row text-center align-items-center flex-row-reverse">
              <div class="col-lg-auto ms-lg-auto">
                <ul class="list-inline list-inline-dots mb-0">
                  <li class="list-inline-item"><a href="https://github.com/tdragon6/Black/blob/main/LICENSE" target="_blank" class="link-secondary" style="text-decoration:none;">License</a></li>
                  <li class="list-inline-item"><a href="https://github.com/tdragon6/Black" target="_blank" class="link-secondary" style="text-decoration:none;">Github</a></li>
                  <li class="list-inline-item">
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon text-pink icon-filled icon-inline" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M19.5 12.572l-7.5 7.428l-7.5 -7.428m0 0a5 5 0 1 1 7.5 -6.566a5 5 0 1 1 7.5 6.572" /></svg>
                      93dc2d41ae035a65
                  </li>
                </ul>
              </div>
              <div class="col-12 col-lg-auto mt-3 mt-lg-0">
                <ul class="list-inline list-inline-dots mb-0">
                  <li class="list-inline-item">
                    Copyright &copy; {{ year }} Black. All rights reserved.
                  </li>
                  <li class="list-inline-item">
                    <a>{{ black_version }}</a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </footer>
      </div>
     </div>
    <script src="/static/js/tabler.min.js"></script>
    <script src="/static/js/jquery.min.js"></script>
    <script src="/static/js/juqery.cookie.min.js"></script>
    <script src="/static/js/clipboard.js"></script>
    <script src="/static/js/jquery.ztree.core.min.js"></script>
    <script src="/static/js/toastr.min.js"></script>
    <script src="/static/js/purify.min.js"></script>
    <script src="/static/js/func/session.js"></script>
    <script src="/static/js/func/func.js"></script>
    <script src="/static/js/func/session_files.js"></script>
    <script>
        // 设置提示框属性
        toastr.options.positionClass = 'toast-top-center';
        toastr.options.timeOut = 3000;
        toastr.options.escapeHtml = true;
    </script>
    <script>
        // 目录树设置
        var tree_setting = {callback: {onExpand: zTreeOnExpand, onClick: zTreeOnClick}};
        var zNodes = [];
        var zTreeObj;
    </script>
    <script>
        // 获取sessid
        var sessid = '{{ sessid | safe }}';

        // 设置菜单栏指向的链接
        set_menu_link(sessid);

        // 设置路径输入框默认当前路径和面包屑路径条
        set_default_path({{ default_path | safe }});

        // 设置目录树根目录
        set_root_folder({{ root_folder | safe }}, tree_setting, zNodes);

        // 原始文件数据分组步长
        var pages_size = 10;
        if ($.cookie('session_files_page_num') !== undefined){
          if (check_positive_integer($.cookie('session_files_page_num'))){
            pages_size = Number($.cookie('session_files_page_num'));
          }
        }
        $('#page-change-num').val(pages_size.toString());

        // 原始列表类型文件数据
        var files_list = [];

        // 原始常用文件数据分组步长
        var server_files_pages_size = 10;
        if ($.cookie('session_server_files_page_num') !== undefined){
          if (check_positive_integer($.cookie('session_server_files_page_num'))){
            server_files_pages_size = Number($.cookie('session_server_files_page_num'));
          }
        }
        $('#page-change-num-upload').val(pages_size.toString());

        // 原始常用文件列表类型文件数据
        var server_files_list = [];

        // 定义获取文件列表请求编号，多次请求覆盖之前请求
        var files_no = 0;

        // 定义获取目录树请求编号，多次请求不会把状态图标置对勾
        var tree_no = 0;

        // 获取文件列表
        get_files_list($('#file_path').val(), sessid)

        //回车提交会话文件列表每页条数配置
        page_enter_listen();

        //回车提交常用文件列表每页条数配置
        page_upload_enter_listen();

        // 键盘监听时间
        files_enter_listen();
    </script>
  </body>
</html>